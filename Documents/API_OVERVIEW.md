# ThreatMesh API Documentation

## Overview

ThreatMesh is a comprehensive cybersecurity intelligence platform that provides Attack Surface Mapping (ASM) and Cyber Threat Intelligence (CTI) capabilities through a RESTful API. The platform helps organizations discover and monitor their digital footprint, identify potential security threats, and gather intelligence from various sources.

### Key Capabilities

- **Attack Surface Mapping**: Discover and enumerate your organization's external attack surface
- **Cyber Threat Intelligence**: Monitor threats, leaked data, and malicious activities targeting your organization
- **Automated Scanning**: Background task processing with real-time status updates
- **Intelligent Caching**: Automatic result caching to optimize performance and reduce redundant scans
- **Comprehensive Data Sources**: Integration with multiple threat intelligence and scanning tools

## API Architecture

### Base Configuration
- **Base URL**: `http://localhost:8000`
- **Authentication**: API key required in `X-API-Key` header
- **Task Processing**: Asynchronous background processing using Celery
- **Caching**: Intelligent caching system with configurable TTL per feature
- **Response Format**: Standardized JSON responses across all endpoints

### Standard Response Format

All API endpoints return a consistent response structure:

```json
{
  "job_id": "unique_task_identifier",
  "status": "pending|running|completed|failed|killed|suspended",
  "feature": "feature_name",
  "start_time": "ISO_timestamp",
  "end_time": "ISO_timestamp",
  "results": {
    // Feature-specific results
  },
  "error": null,  // Present only on failure
  "progress_perc": 100,  // 0-100
  "cache_hit": true  // Present only when returning cached results
}
```

## API Categories

### 1. Attack Surface Mapping (ASM) APIs

The ASM module helps organizations discover and map their external attack surface by identifying assets, services, and potential entry points.

#### Available Endpoints:
- **Subdomain Enumeration** (`/asm/subdomains`) - Discover subdomains using multiple sources
- **Public IP Discovery** (`/asm/public_ips`) - Find public IPs with service and vulnerability data
- **Technology Detection** (`/asm/tech_detection`) - Identify technologies used by web servers
- **Web Server Discovery** (`/asm/webservers`) - Discover and analyze web servers

#### Core Features:
- Multi-source data aggregation (Shodan, Subfinder, etc.)
- Service and vulnerability identification
- Technology stack detection
- Comprehensive asset inventory

### 2. Cyber Threat Intelligence (CTI) APIs

The CTI module provides threat intelligence by monitoring various sources for mentions of your organization, leaked data, and potential threats.

#### Available Endpoints:

- **Phishing Domains** (`/cti/phishing_domains`) - Detect phishing domains using domain permutation algorithms
- **Git Repository Search** (`/cti/git_search`) - Search for secrets and credentials in public repositories
- **Leaked Pastes** (`/cti/leaked_pastes`) - Monitor paste sites for leaked information
- **Dark Web Monitoring** (`/cti/darkweb`) - Search dark web forums and marketplaces

#### Core Features:
- Multi-source threat intelligence aggregation
- Advanced domain fuzzing algorithms (16 different techniques)
- Credential and secret detection
- Dark web and surface web monitoring
- Comprehensive data enrichment

## Task Management

### Task Lifecycle
1. **Initiation**: Task is created and queued for processing
2. **Processing**: Background worker executes the task
3. **Completion**: Results are stored and cached
4. **Retrieval**: Results can be retrieved using job_id

### Status Management
- **pending**: Task waiting in queue
- **running**: Task currently executing
- **completed**: Task finished successfully
- **failed**: Task encountered an error
- **killed**: Task was terminated
- **suspended**: Task temporarily paused

### Caching Strategy
Intelligent caching reduces redundant scans and improves response times:

| Feature | Cache Duration | Purpose |
|---------|----------------|---------|
| Subdomain Enumeration | 7 hours | DNS changes are infrequent |
| Public IP Discovery | 1 hour | IP assignments change regularly |
| Leaked Pastes | 3 days | New pastes accumulate slowly |
| Dark Web Monitoring | 3 days | Dark web content updates slowly |

| Phishing Domains | 12 hours | New registrations occur frequently |

## Authentication

All API endpoints require authentication using an API key:

```http
X-API-Key: your_secret_api_key
```

## Request Parameters

### Common Parameters
- `job_id` (optional): Retrieve results from a specific task
- `ignore_cache` (optional): Force new scan bypassing cached results

### Feature-Specific Parameters
Each endpoint has specific required parameters (domain, keyword, etc.) detailed in the endpoint documentation below.

## Feature Documentation

For detailed API specifications, request/response examples, and implementation details for each feature, please refer to the dedicated documentation files:

### Attack Surface Mapping (ASM)
- [Subdomain Analysis](Features/SUBDOMAIN_ANALYSIS.md) - Subdomain enumeration and discovery
- [IP Discovery](Features/IP_DISCOVERY.md) - Public IP discovery and enrichment
- [Technology Detection](Features/TECH_DETECTION.md) - Web technology stack identification
- [Web Server Discovery](Features/WEBSERVER_DISCOVERY.md) - Web server discovery and analysis

### Cyber Threat Intelligence (CTI)

- [Phishing Domains](Features/PHISHING_DOMAINS.md) - Domain permutation and phishing detection
- [Git Search](Features/GIT_SEARCH.md) - Public repository credential scanning
- [Pastes Search](Features/PASTES_SEARCH.md) - Leaked information monitoring
- [Dark Web Leaks](Features/DARKWEB_LEAKS.md) - Dark web threat intelligence

## Deployment and Configuration

For deployment instructions, configuration options, and operational details, see:
- [Database Schema](DATABASE_SCHEMA.md) - Database structure and relationships
- [README](../README.md) - Setup and deployment instructions
