# Technology Detection API

## Overview
The Technology Detection API provides comprehensive technology stack analysis for web applications using Wappalyzer. It identifies technologies, frameworks, libraries, and services used by websites.

## Features
- **Automatic Detection**: Uses Wappalyzer for comprehensive technology identification
- **Dual Workflow**: Domain-based analysis using stored webserver data or direct URL analysis
- **Complete Data**: Full technology details including confidence scores, versions, and categories
- **Performance Optimized**: Intelligent caching and error handling
- **Integration Ready**: Seamless integration with webserver discovery results

## Data Flow Explanation

### Complete Request Flow - Domain-Based Analysis

**1. Receive API request with domain + parameters**
```http
GET /asm/tech-detection?domain=example.com&ignore_cache=false
```
↓

**2. Validate request parameters (domain, job_id, ignore_cache)**
```python
# Validated parameters
{
    "domain": "example.com",
    "job_id": None,
    "ignore_cache": False
}
```
↓

**3. Check for existing job_id or create new task**
```python
# If job_id exists, return cached result
# If new request, generate new job_id
job_id = "tech-abc123-def456-ghi789"
```
↓

**4. Retrieve webserver discovery results for the domain**
```python
# Query database for webserver results
webserver_results = get_webserver_results(domain="example.com")
urls_to_analyze = [
    "https://api.example.com",
    "https://blog.example.com", 
    "https://admin.example.com"
]
```
↓

**5. Create Celery background task job**
```python
task = run_tech_detection.delay(
    urls=urls_to_analyze,
    job_id="tech-abc123-def456-ghi789"
)
```
↓

**6. Execute Wappalyzer analysis for each URL:**
- Tool: Wappalyzer CLI (technology detection tool)
- Timeout: 120 seconds per URL
- Analysis: HTTP headers, DOM structure, JavaScript patterns
- Coverage: 1,400+ technologies across 70+ categories
- Command parameters:
  - `-p full`: Perform a deeper scan by performing additional requests and inspecting DNS records
  - `-a "..."`: Custom user agent string
  - `-D 5`: Don't analyse pages more than num levels deep
  - `-m 30`: Exit when num URLs have been analysed
  - `-w 10000`: Wait no more than ms milliseconds for page resources to load
  - `-r`: Follow links on pages (crawler)

```bash
# For each URL
node /opt/wappalyzer/src/drivers/npm/cli.js https://api.example.com -p full -a "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" -D 5 -m 30 -w 10000 -r
node /opt/wappalyzer/src/drivers/npm/cli.js https://blog.example.com -p full -a "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" -D 5 -m 30 -w 10000 -r
node /opt/wappalyzer/src/drivers/npm/cli.js https://admin.example.com -p full -a "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" -D 5 -m 30 -w 10000 -r
```
↓

**7. Wappalyzer executes comprehensive technology detection:**
- HTTP response header analysis
- HTML/DOM structure inspection  
- JavaScript library detection
- CSS framework identification
- Meta tag analysis
- Cookie and localStorage inspection

```json
# Sample raw output from Wappalyzer
{
  "url": "https://blog.example.com",
  "technologies": [
    {
      "slug": "wordpress",
      "name": "WordPress", 
      "confidence": 100,
      "version": "6.8.1",
      "categories": [{"id": 1, "slug": "cms", "name": "CMS"}]
    },
    {
      "slug": "php",
      "name": "PHP",
      "confidence": 100,
      "version": "8.2.27",
      "categories": [{"id": 27, "slug": "programming-languages", "name": "Programming languages"}]
    }
  ]
}
```
↓

**8. Parse and process Wappalyzer results:**
- Aggregate technology data across all URLs
- Calculate statistics and counts
- Handle failed analysis gracefully
- Track success/failure rates

```python
# Processed result structure
{
    "tech_detections": [
        {
            "url": "https://blog.example.com",
            "host": "blog.example.com", 
            "status": 200,
            "tech_count": 5,
            "technologies": [
                {
                    "slug": "wordpress",
                    "name": "WordPress",
                    "confidence": 100,
                    "version": "6.8.1",
                    "categories": [{"id": 1, "slug": "cms", "name": "CMS"}]
                }
            ]
        }
    ],
    "statistics": {
        "total_urls_analyzed": 3,
        "successful_detections": 3,
        "failed_detections": 0,
        "unique_technologies": ["wordpress", "php", "nginx"],
        "unique_tech_count": 3
    }
}
```
↓

**9. Store results in database:**
- Store complete technology detection data
- Store analysis statistics and metadata
- Clear intermediate processing data
- Update task status and completion time

```python
# Database storage structure
{
    "job_id": "tech-abc123-def456-ghi789",
    "status": "COMPLETED",
    "feature": "tech_detection",
    "structured_output": {
        "tech_detections": [...],
        "count": 3,
        "statistics": {
            "total_urls_analyzed": 3,
            "successful_detections": 3,
            "failed_detections": 0,
            "unique_technologies": ["wordpress", "php", "nginx"],
            "unique_tech_count": 3
        }
    },
    "raw_output": []  # Cleared to save space
}
```
↓

**10. Return response to client:**
- If task pending: return job_id + status + progress
- If completed: return technology detections + statistics
- Support cache_hit for repeated requests

```json
{
    "job_id": "tech-abc123-def456-ghi789",
    "status": "completed",
    "feature": "tech_detection",
    "message": "Technology detection completed for 3 URLs. Successful: 3, Failed: 0",
    "start_time": "2024-01-20T12:00:00Z",
    "end_time": "2024-01-20T12:05:30Z",
    "execution_time": 330,
    "data": {
        "tech_detections": [
            {
                "url": "https://blog.example.com",
                "host": "blog.example.com",
                "status": 200,
                "tech_count": 5,
                "technologies": [
                    {
                        "slug": "wordpress",
                        "name": "WordPress",
                        "confidence": 100,
                        "version": "6.8.1",
                        "categories": [{"id": 1, "slug": "cms", "name": "CMS"}]
                    }
                ]
            }
        ],
        "count": 3,
        "statistics": {
            "total_urls_analyzed": 3,
            "successful_detections": 3,
            "failed_detections": 0,
            "unique_technologies": ["wordpress", "php", "nginx"],
            "unique_tech_count": 3
        }
    },
    "cache_hit": false
}
```

### Complete Request Flow - Direct URL Analysis

**1. Receive API request with URL list**
```http
POST /asm/tech-detection?ignore_cache=false
Content-Type: application/json

{
  "urls": [
    "https://api.example.com",
    "https://blog.example.com"
  ]
}
```
↓

**2. Validate request parameters and URL list**
```python
# Validated parameters
{
    "urls": ["https://api.example.com", "https://blog.example.com"],
    "job_id": None,
    "ignore_cache": False
}
# URL validation: HTTP/HTTPS format, max 100 URLs
```
↓

**3. Generate job_id and check cache**
```python
# Generate unique job_id for this URL set
job_id = "tech-xyz789-abc123-def456"

# Check cache based on URL list hash
cache_key = hash(sorted(urls))
```
↓

**4. Create Celery background task job**
```python
task = run_tech_detection.delay(
    urls=["https://api.example.com", "https://blog.example.com"],
    job_id="tech-xyz789-abc123-def456"
)
```
↓

**5-10. Same Wappalyzer execution and processing steps as domain-based flow**

### Key Components Interaction

**API Layer** (`app/routers/asm/tech_detection.py`)
- Handles HTTP requests for `/asm/tech-detection`
- Validates domain parameters and URL lists
- Manages task lifecycle and caching
- Integrates with webserver discovery results
- Returns formatted technology analysis responses

**Task Layer** (`app/tasks/asm/tech_detection.py`) 
- Executes Wappalyzer commands asynchronously
- Processes JSON output from Wappalyzer
- Handles parallel URL analysis and error recovery
- Updates task status and stores comprehensive results

**Detection Tool** (`Wappalyzer CLI`)
- Open source technology detection tool
- Analyzes web technologies through multiple methods
- Outputs structured JSON with confidence scores
- Supports 1,400+ technologies across 70+ categories

**Data Storage**
- MongoDB for result persistence
- Structured output with technology details and statistics
- Raw output cleared after processing to save space
- Cache expiration: 30 days (configurable)

### Wappalyzer Detection Methods

**HTTP Header Analysis**
- Server headers (nginx, Apache, IIS)
- X-Powered-By headers (PHP, ASP.NET)
- Custom application headers
- Security headers and configurations

**DOM/HTML Analysis**
- Meta tags and generators
- HTML structure patterns
- CSS class naming conventions
- Favicon fingerprinting

**JavaScript Detection**
- Library-specific global variables
- Function signatures and patterns
- Framework-specific code structures
- Third-party service integrations

**Technology Categories**
- Web servers (nginx, Apache, IIS)
- Programming languages (PHP, Python, Node.js)
- CMS platforms (WordPress, Drupal, Joomla)
- E-commerce (Shopify, Magento, WooCommerce)
- Analytics (Google Analytics, Adobe Analytics)
- CDN services (Cloudflare, AWS CloudFront)

### Cache Management

**Cache Duration: 30 days**
```python
# Cache configuration
TASK_EXPIRATION = {
    "tech_detection": timedelta(days=30)
}

# Cache behavior
- Running tasks: Always return existing task
- Completed tasks: Return cached results within 30 days
- Expired cache: Start new technology analysis
- ignore_cache=true: Force new analysis regardless of cache
```

**Cache Benefits:**
- Reduces redundant Wappalyzer analysis
- Faster response times for recent technology scans
- Preserves computational resources
- Maintains consistency in technology detection over time

## API Endpoints

### 1. GET /asm/tech-detection

#### Option A: Check Status
```bash
GET /asm/tech-detection?job_id=abc123
```

#### Option B: Domain Analysis (Uses Webserver Discovery Results)
```bash
GET /asm/tech-detection?domain=example.com&ignore_cache=false&url=https://api.example.com&page=1&records_per_page=100
```

**Parameters:**
- `job_id` (optional): Existing job ID to check status or get results
- `domain` (optional): Domain to analyze using stored webserver URLs
- `ignore_cache` (optional): Force fresh analysis (default: false)
- `url` (optional): Filter results by specific URL within the domain
- `page` (optional): Page number for paginated results (default: 1)
- `records_per_page` (optional): Number of records per page (default: 100, max: 1000)

**URL Filtering:**
- When both `domain` and `url` parameters are provided, the system will:
  1. Look for cached results for the domain
  2. Filter those results to show only the specified URL
  3. Apply pagination to the filtered results
- This allows efficient retrieval of technology detection data for specific URLs within a domain

**Cache Behavior:**
- Results cached for 30 days by default
- Use `ignore_cache=true` to force fresh analysis
- URL filtering works on cached results for optimal performance

### 2. POST /asm/tech-detection

```bash
POST /asm/tech-detection?job_id=abc123&ignore_cache=false&url=https://api.example.com&page=1&records_per_page=100
Content-Type: application/json

{
  "urls": [
    "https://api.example.com",
    "https://blog.example.com",
    "https://admin.example.com"
  ]
}
```

**Parameters:**
- `job_id` (optional): Custom job ID for this analysis
- `ignore_cache` (optional): Force fresh analysis (default: false)
- `url` (optional): Filter results by specific URL from the provided URL list
- `page` (optional): Page number for paginated results (default: 1)
- `records_per_page` (optional): Number of records per page (default: 100, max: 1000)

**Request Body:**
- `urls`: Array of URLs to analyze (max 100)
- URLs must be valid HTTP/HTTPS format

**URL Filtering:**
- When `url` parameter is provided with a POST request, the system will:
  1. Execute or retrieve the technology detection for all provided URLs
  2. Filter results to show only the specified URL
  3. Apply pagination to the filtered results

## Response Format

### Success Response
```json
{
  "job_id": "abc123-def456-ghi789",
  "status": "completed",
  "feature": "tech_detection",
  "message": "Technology detection completed for 3 URLs. Successful: 3, Failed: 0",
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:32:45Z",
  "start_time": "2024-01-15T10:30:05Z",
  "end_time": "2024-01-15T10:32:45Z",
  "progress_perc": 100,
  "cache_hit": false,
  "results": {
    "tech_detections": [
      {
        "url": "https://example.com",
        "host": "example.com",
        "http_status_code": "200",
        "tech_count": 5,
        "technologies": [
          {
            "slug": "wordpress",
            "name": "WordPress",
            "description": "WordPress is a free and open-source content management system written in PHP and paired with a MySQL or MariaDB database.",
            "confidence": 100,
            "version": "6.8.1",
            "icon": "WordPress.svg",
            "website": "https://wordpress.org",
            "cpe": "cpe:2.3:a:wordpress:wordpress:*:*:*:*:*:*:*:*",
            "categories": [
              {
                "id": 1,
                "slug": "cms",
                "name": "CMS"
              },
              {
                "id": 11,
                "slug": "blogs",
                "name": "Blogs"
              }
            ],
            "rootPath": true
          },
          {
            "slug": "php",
            "name": "PHP",
            "description": "PHP is a general-purpose scripting language used for web development.",
            "confidence": 100,
            "version": "8.2.27",
            "icon": "PHP.svg",
            "website": "http://php.net",
            "cpe": "cpe:2.3:a:php:php:*:*:*:*:*:*:*:*",
            "categories": [
              {
                "id": 27,
                "slug": "programming-languages",
                "name": "Programming languages"
              }
            ],
            "rootPath": true
          }
        ]
      }
    ],
    "pagination": {
      "total_records": 150,
      "page": 1,
      "total_pages": 2,
      "records_per_page": 100
    },
    "statistics": {
      "total_urls_analyzed": 150,
      "total_detections": 150,
      "total_chunks": 2,
      "successful_detections": 145,
      "failed_detections": 5,
      "unique_technologies": ["wordpress", "php", "nginx", "mysql", "javascript"],
      "unique_tech_count": 5
    }
  }
}
```

### Success Response with URL Filter
```json
{
  "job_id": "abc123-def456-ghi789",
  "status": "completed",
  "feature": "tech_detection",
  "cache_hit": true,
  "results": {
    "tech_detections": [
      {
        "url": "https://api.example.com",
        "host": "api.example.com",
        "http_status_code": "200",
        "tech_count": 3,
        "technologies": [
          {
            "slug": "node-js",
            "name": "Node.js",
            "confidence": 100,
            "version": "18.17.0"
          }
        ]
      }
    ],
    "pagination": {
      "total_records": 1,
      "page": 1,
      "total_pages": 1,
      "records_per_page": 100
    },
    "url_filter": "https://api.example.com",
    "statistics": {
      "total_urls_analyzed": 150,
      "total_detections": 150,
      "total_chunks": 2,
      "successful_detections": 145,
      "failed_detections": 5,
      "unique_technologies": ["wordpress", "php", "nginx", "mysql", "javascript"],
      "unique_tech_count": 5
    }
  }
}
```

### In Progress Response
```json
{
  "job_id": "abc123-def456-ghi789",
  "status": "running",
  "feature": "tech_detection",
  "message": "Analyzing https://example.com (1/3)",
  "progress_perc": 45,
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:31:15Z",
  "start_time": "2024-01-15T10:30:05Z"
}
```

### Error Response
```json
{
  "job_id": "abc123-def456-ghi789",
  "status": "failed",
  "feature": "tech_detection",
  "message": "No webserver discovery results found for domain example.com. Please run webserver discovery first.",
  "error": "No webserver discovery results found for domain example.com",
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:30:15Z",
  "end_time": "2024-01-15T10:30:15Z"
}
```

## Data Structure

### Technology Object
Each detected technology includes:
- `slug`: Unique identifier for the technology
- `name`: Human-readable name
- `description`: Detailed description
- `confidence`: Detection confidence (0-100)
- `version`: Detected version (if available)
- `icon`: Icon filename
- `website`: Official website URL
- `cpe`: Common Platform Enumeration identifier
- `categories`: Array of category objects
- `rootPath`: Whether detected at root path

### Category Object
- `id`: Unique category identifier
- `slug`: Category slug
- `name`: Category display name

## Workflows

### Workflow 1: Domain-Based Analysis
1. **Run Webserver Discovery** first to identify active web services
2. **Run Technology Detection** using the domain parameter
3. The system automatically uses URLs from webserver discovery results
4. Get comprehensive technology analysis for all discovered web services

```bash
# Step 1: Discover webservers
POST /asm/webserver-discovery
{
  "subdomains": ["example.com", "api.example.com", "blog.example.com"]
}

# Step 2: Analyze technologies (uses webserver results automatically)
GET /asm/tech-detection?domain=example.com
```

### Workflow 2: Direct URL Analysis
1. **Provide URL List** directly in POST request
2. Get immediate technology analysis for specified URLs

```bash
POST /asm/tech-detection
{
  "urls": ["https://api.example.com", "https://blog.example.com"]
}
```

## Technical Details

### Technology Detection Engine
- **Tool**: Wappalyzer CLI
- **Timeout**: 120 seconds per URL
- **Detection Method**: HTTP response analysis, DOM inspection, header analysis
- **Coverage**: 1,400+ technologies across 70+ categories

### Performance Characteristics
- **Parallel Processing**: Multiple URLs analyzed concurrently
- **Timeout Handling**: Graceful handling of slow responses
- **Error Recovery**: Continues analysis even if individual URLs fail
- **Memory Efficient**: Streams results to database in real-time

### Cache Strategy
- **Cache Duration**: 30 days for technology detection results
- **Cache Key**: Based on exact URL list or domain
- **Cache Bypass**: Use `ignore_cache=true` parameter
- **Cache Benefits**: Significant performance improvement for repeated analysis

## Integration Examples

### Python Integration
```python
import requests
import time

# Domain-based analysis
response = requests.get(
    "http://localhost:8000/asm/tech-detection",
    params={"domain": "example.com"}
)

job_id = response.json()["job_id"]

# Poll for completion
while True:
    status_response = requests.get(
        "http://localhost:8000/asm/tech-detection",
        params={"job_id": job_id}
    )
    
    status_data = status_response.json()
    if status_data["status"] in ["completed", "failed"]:
        break
    
    time.sleep(5)

# Process results
if status_data["status"] == "completed":
    tech_detections = status_data["results"]["tech_detections"]
    for detection in tech_detections:
        print(f"URL: {detection['url']}")
        print(f"Technologies: {[t['name'] for t in detection['technologies']]}")

# Example: URL filtering with pagination
filtered_response = requests.get(
    "http://localhost:8000/asm/tech-detection",
    params={
        "domain": "example.com",
        "url": "https://api.example.com",
        "page": 1,
        "records_per_page": 50
    }
)

if filtered_response.json()["status"] == "completed":
    results = filtered_response.json()["results"]
    print(f"Filtered results for {results.get('url_filter', 'all URLs')}")
    print(f"Page {results['pagination']['page']} of {results['pagination']['total_pages']}")
    print(f"Total records: {results['pagination']['total_records']}")
```

### cURL Examples
```bash
# Check status
curl "http://localhost:8000/asm/tech-detection?job_id=abc123"

# Domain analysis
curl "http://localhost:8000/asm/tech-detection?domain=example.com"

# Domain analysis with URL filtering
curl "http://localhost:8000/asm/tech-detection?domain=example.com&url=https://api.example.com"

# Domain analysis with pagination
curl "http://localhost:8000/asm/tech-detection?domain=example.com&page=2&records_per_page=50"

# Domain analysis with URL filter and pagination
curl "http://localhost:8000/asm/tech-detection?domain=example.com&url=https://api.example.com&page=1&records_per_page=10"

# Direct URL analysis
curl -X POST "http://localhost:8000/asm/tech-detection" \
  -H "Content-Type: application/json" \
  -d '{"urls": ["https://example.com", "https://api.example.com"]}'

# Direct URL analysis with URL filtering
curl -X POST "http://localhost:8000/asm/tech-detection?url=https://api.example.com&page=1&records_per_page=50" \
  -H "Content-Type: application/json" \
  -d '{"urls": ["https://example.com", "https://api.example.com"]}'

# Force fresh analysis
curl "http://localhost:8000/asm/tech-detection?domain=example.com&ignore_cache=true"
```

### URL Filtering Examples
```bash
# Get all technology detections for a domain
curl "http://localhost:8000/asm/tech-detection?domain=example.com"

# Filter results to show only a specific URL
curl "http://localhost:8000/asm/tech-detection?domain=example.com&url=https://api.example.com"

# Combine URL filtering with pagination for large datasets
curl "http://localhost:8000/asm/tech-detection?domain=example.com&url=https://blog.example.com&page=1&records_per_page=25"

# Filter results from a POST request
curl -X POST "http://localhost:8000/asm/tech-detection?url=https://api.example.com" \
  -H "Content-Type: application/json" \
  -d '{"urls": ["https://example.com", "https://api.example.com", "https://blog.example.com"]}'
```

## Error Handling

### Common Error Scenarios
1. **No Webserver Results**: Domain analysis when no webserver discovery data exists
2. **Invalid URLs**: Malformed URLs in POST requests
3. **Empty URL List**: No URLs provided or extracted
4. **Wappalyzer Timeout**: Analysis timeout for slow-responding URLs
5. **Tool Unavailable**: Wappalyzer CLI not accessible

### Error Prevention
- **Validation**: Input validation for URLs and parameters
- **Dependencies**: Clear error messages about required prerequisites
- **Graceful Degradation**: Continue analysis even if some URLs fail
- **Comprehensive Logging**: Detailed error information for debugging

## Security Considerations

### Input Validation
- URL format validation (HTTP/HTTPS only)
- Maximum URL limit enforcement (100 URLs)
- Domain validation for domain-based analysis

### External Tool Security
- Sandboxed execution of Wappalyzer
- Timeout enforcement to prevent hanging
- Error handling for malicious responses

### Data Privacy
- No sensitive data stored in technology detection results
- URLs and technology information only
- Configurable data retention periods 