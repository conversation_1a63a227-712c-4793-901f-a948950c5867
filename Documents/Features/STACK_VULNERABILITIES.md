# Stack Vulnerabilities Analysis API

## Overview
The Stack Vulnerabilities Analysis API provides comprehensive vulnerability assessment for technology stacks using data from the National Vulnerability Database (NVD). It identifies Common Vulnerabilities and Exposures (CVEs) associated with detected technologies and enriches them with public exploit information.

## Features
- **Dual Analysis Mode**: Domain-based analysis using stored tech detection results or direct CPE analysis
- **NVD Integration**: Real-time CVE data from the National Vulnerability Database
- **EPSS Integration**: Exploit Prediction Scoring System scores from FIRST.org API for exploitation probability
- **Exploit Intelligence**: Integration with ExploitDB, GitHub POCs, and Nuclei templates
- **CVSS Scoring**: Complete CVSS metrics with severity prioritization (V4.0 > V3.1 > V3.0 > V2.0)
- **KEV Tracking**: Identifies vulnerabilities in CISA's Known Exploited Vulnerabilities catalog
- **Performance Optimized**: Intelligent caching, bulk API calls, rate limiting, and error handling
- **Integration Ready**: Seamless integration with technology detection results

## Data Flow Explanation

### Complete Request Flow - Domain-Based Analysis

**1. Receive API request with domain + parameters**
```http
GET /asm/stack-vulns?domain=example.com&ignore_cache=false
```
↓

**2. Validate request parameters (domain, cpe, job_id, ignore_cache)**
```python
# Validated parameters
{
    "domain": "example.com",
    "job_id": None,
    "ignore_cache": False
}
```
↓

**3. Check for existing job_id or create new task**
```python
# If job_id exists, return cached result
# If new request, generate new job_id
job_id = "stack-abc123-def456-ghi789"
```
↓

**4. Retrieve tech detection results and extract CPE information**
```python
# Query database for recent tech detection results
tech_results = get_tech_detection_results(domain="example.com")
cpe_list = [
    {
        "cpe": "cpe:2.3:a:wordpress:wordpress:6.8.1:*:*:*:*:*:*:*",
        "host": "blog.example.com",
        "tech_name": "WordPress",
        "tech_version": "6.8.1"
    },
    {
        "cpe": "cpe:2.3:a:php:php:8.2.27:*:*:*:*:*:*:*",
        "host": "blog.example.com", 
        "tech_name": "PHP",
        "tech_version": "8.2.27"
    }
]
```
↓

**5. Create Celery background task job**
```python
task = run_stack_vulnerabilities.delay(
    domain="example.com",
    cpe=None,
    job_id="stack-abc123-def456-ghi789"
)
```
↓

**6. Execute NVD API queries for each CPE:**
- API: NVD CVE API 2.0 (services.nvd.nist.gov)
- Authentication: API key (if available) for higher rate limits
- Rate limiting: Built-in retry logic with exponential backoff
- Timeout: 60 seconds per request

```python
# For each CPE in the list
params = {"cpeName": "cpe:2.3:a:wordpress:wordpress:6.8.1:*:*:*:*:*:*:*"}
response = requests.get(
    "https://services.nvd.nist.gov/rest/json/cves/2.0",
    params=params,
    headers={"apiKey": "YOUR_API_KEY"}
)
```
↓

**7. NVD API returns CVE data with comprehensive metrics:**
- CVE descriptions and details
- CVSS metrics (V4.0, V3.1, V3.0, V2.0)
- Weakness (CWE) information
- Publication and modification dates
- CISA KEV status

```json
# Sample NVD API response
{
  "vulnerabilities": [
    {
      "cve": {
        "id": "CVE-2024-1234",
        "descriptions": [
          {
            "lang": "en",
            "value": "WordPress Core is vulnerable to SQL Injection..."
          }
        ],
        "metrics": {
          "cvssMetricV31": [
            {
              "type": "Primary",
              "cvssData": {
                "baseScore": 9.8,
                "baseSeverity": "CRITICAL"
              }
            }
          ]
        },
        "weaknesses": [
          {
            "type": "Primary",
            "description": [{"lang": "en", "value": "CWE-89"}]
          }
        ],
        "published": "2024-01-15T10:15:00.000",
        "lastModified": "2024-01-20T14:30:00.000",
        "cisaExploitAdd": "2024-01-16"
      }
    }
  ]
}
```
↓

**8. Process and enrich CVE data:**
- Extract severity using latest CVSS version available
- Process weakness (CWE) information with primary/secondary prioritization
- Set KEV status based on cisaExploitAdd field
- Standardize date formats

```python
# Processed CVE structure
vulnerability = {
    "cve_id": "CVE-2024-1234",
    "description": "WordPress Core is vulnerable to SQL Injection...",
    "severity": "CRITICAL",
    "weakness(CWE-ID)": "CWE-89",
    "published": "2024-01-15T10:15:00.000",
    "lastModified": "2024-01-20T14:30:00.000",
    "KEV": True,
    "metrics": {...},  # Complete CVSS metrics
    "nvd_link": "https://nvd.nist.gov/vuln/detail/CVE-2024-1234"
}
```
↓

**9. Bulk fetch EPSS scores for all CVEs:**
- **FIRST.org EPSS API**: Bulk query for exploitation probability scores
- **Batch Processing**: Groups CVEs in batches of 50 for optimal performance
- **Smart Caching**: Caches results to avoid duplicate API calls

```python
# Collect all CVE IDs from all CPEs first
all_cve_ids = set()
for cpe_info in cpe_list:
    cve_data = _fetch_cve_details_by_cpe(cpe_info["cpe"])
    for cve in cve_data:
        all_cve_ids.add(cve["cve_id"])

# Bulk fetch EPSS scores (batches of 50 CVEs)
_fetch_epss_scores_bulk(list(all_cve_ids))

# EPSS API request example
params = {
    'cve': 'CVE-2024-1234,CVE-2024-5678,CVE-2024-7890',
    'pretty': 'true'
}
response = requests.get("https://api.first.org/data/v1/epss", params=params)
```
↓

**10. Fetch exploit intelligence for each CVE:**
- **ExploitDB**: Search CSV database + Trickest fallback
- **GitHub POCs**: Extract from Trickest community data
- **Nuclei Templates**: Search ProjectDiscovery template database

```python
# Multi-source exploit search (optimized single call)
exploitdb_links, github_pocs, nuclei_templates = _get_all_exploit_links("CVE-2024-1234")

# ExploitDB search in CSV
exploit_db = get_exploit_db_csv()
for exp_id, data in exploit_db.items():
    if "cve-2024-1234" in data.get('codes', '').lower():
        exploitdb_links.append(f"https://www.exploit-db.com/exploits/{exp_id}")

# Trickest community intelligence
trickest_url = f"https://raw.githubusercontent.com/trickest/cve/refs/heads/main/2024/CVE-2024-1234.md"
trickest_content = requests.get(trickest_url).text

# Extract GitHub POCs from GitHub section
github_section = re.search(r'(?i)^#+\s*github.*?\n(.*?)(?=^#+|\Z)', content, re.MULTILINE | re.DOTALL)
github_links = re.findall(r'https://github\.com/[^\s)]+', github_section.group(1))

# Nuclei templates search
nuclei_templates_db = get_nuclei_templates()
if "cve-2024-1234" in nuclei_templates_db:
    template_path = nuclei_templates_db["cve-2024-1234"]["file_path"]
    nuclei_templates.append(f"https://github.com/projectdiscovery/nuclei-templates/blob/main/{template_path}")
```
↓

**11. Combine vulnerability, exploit, and EPSS data:**
```python
# Enhanced vulnerability with exploit intelligence and EPSS scores
vulnerability["public_exploits"] = {
    "exploitDB": [
        "https://www.exploit-db.com/exploits/51234",
        "https://www.exploit-db.com/exploits/51235"
    ],
    "nuclei_templates": [
        "https://github.com/projectdiscovery/nuclei-templates/blob/main/cves/2024/CVE-2024-1234.yaml"
    ],
    "potential_github_pocs": [
        "https://github.com/security-researcher/CVE-2024-1234-poc",
        "https://github.com/exploit-dev/wordpress-sqli-poc"
    ]
}

# Add EPSS score from cache
vulnerability["epss_score"] = {
    "epss": 0.89234,        # Exploitation probability (0-1)
    "percentile": 0.99876,  # Percentile ranking
    "date": "2024-01-21"    # Date of EPSS data
}
```
↓

**12. Aggregate results by CPE and calculate statistics:**
```python
# Complete result structure per CPE
stack_vulnerability = {
    "cpe": "cpe:2.3:a:wordpress:wordpress:6.8.1:*:*:*:*:*:*:*",
    "host": "blog.example.com",
    "tech_name": "WordPress",
    "tech_version": "6.8.1",
    "vulnerability_count": 3,
    "vulnerabilities": [
        {
            "cve_id": "CVE-2024-1234",
            "description": "WordPress Core is vulnerable to SQL Injection...",
            "severity": "CRITICAL",
            "weakness(CWE-ID)": "CWE-89",
            "published": "2024-01-15T10:15:00.000",
            "lastModified": "2024-01-20T14:30:00.000", 
            "KEV": True,
            "metrics": {...},
            "nvd_link": "https://nvd.nist.gov/vuln/detail/CVE-2024-1234",
            "public_exploits": {
                "exploitDB": ["https://www.exploit-db.com/exploits/51234"],
                "nuclei_templates": ["https://github.com/projectdiscovery/nuclei-templates/blob/main/cves/2024/CVE-2024-1234.yaml"],
                "potential_github_pocs": ["https://github.com/security-researcher/CVE-2024-1234-poc"]
            },
            "epss_score": {
                "epss": 0.89234,
                "percentile": 0.99876,
                "date": "2024-01-21"
            }
        }
    ]
}

# Overall statistics including EPSS data
statistics = {
    "total_cpes_analyzed": 2,
    "successful_analyses": 2,
    "failed_analyses": 0,
    "total_vulnerabilities": 5,
    "severity_breakdown": {
        "critical": 1,
        "high": 2,
        "medium": 1,
        "low": 1
    },
    "epss_statistics": {
        "total_with_epss": 4,
        "total_without_epss": 1,
        "coverage_percentage": 80.0,
        "high_risk_count": 2,        # EPSS > 0.5
        "average_epss": 0.4092,
        "max_epss": 0.8923,
        "min_epss": 0.0023
    }
}
```
↓

**12. Store results in database:**
- Store complete vulnerability analysis data
- Store statistics and metadata
- Clear raw output to save space
- Update task status and completion time

```python
# Database storage structure
{
    "job_id": "stack-abc123-def456-ghi789",
    "status": "COMPLETED",
    "feature": "stack_vulnerabilities",
    "structured_output": {
        "stack_vulnerabilities": [...],
        "statistics": {
            "total_cpes_analyzed": 2,
            "successful_analyses": 2,
            "failed_analyses": 0,
            "total_vulnerabilities": 5,
            "severity_breakdown": {
                "critical": 1,
                "high": 2,
                "medium": 1,
                "low": 1
            },
            "epss_statistics": {
                "total_with_epss": 4,
                "total_without_epss": 1,
                "coverage_percentage": 80.0,
                "high_risk_count": 2,
                "average_epss": 0.4092,
                "max_epss": 0.8923,
                "min_epss": 0.0023
            }
        }
    },
    "raw_output": []  # Cleared to save space
}
```
↓

**13. Return response to client:**
- If task pending: return job_id + status + progress
- If completed: return vulnerability analysis + statistics  
- Support cache_hit for repeated requests

```json
{
    "job_id": "stack-abc123-def456-ghi789",
    "status": "completed",
    "feature": "stack_vulnerabilities",
    "message": "Stack vulnerabilities analysis completed. Found 5 vulnerabilities across 2 CPEs. EPSS scores retrieved for 4 CVEs.",
    "start_time": "2024-01-20T12:00:00Z",
    "end_time": "2024-01-20T12:03:45Z",
    "execution_time": 225,
    "data": {
        "stack_vulnerabilities": [
            {
                "cpe": "cpe:2.3:a:wordpress:wordpress:6.8.1:*:*:*:*:*:*:*",
                "host": "blog.example.com",
                "tech_name": "WordPress", 
                "tech_version": "6.8.1",
                "vulnerability_count": 3,
                "vulnerabilities": [
                    {
                        "cve_id": "CVE-2024-1234",
                        "description": "WordPress Core is vulnerable to SQL Injection...",
                        "severity": "CRITICAL",
                        "weakness(CWE-ID)": "CWE-89",
                        "published": "2024-01-15T10:15:00.000",
                        "lastModified": "2024-01-20T14:30:00.000",
                        "KEV": true,
                        "metrics": {
                            "cvssMetricV31": [
                                {
                                    "type": "Primary",
                                    "cvssData": {
                                        "baseScore": 9.8,
                                        "baseSeverity": "CRITICAL"
                                    }
                                }
                            ]
                        },
                        "nvd_link": "https://nvd.nist.gov/vuln/detail/CVE-2024-1234",
                        "public_exploits": {
                            "exploitDB": [
                                "https://www.exploit-db.com/exploits/51234"
                            ],
                            "nuclei_templates": [
                                "https://github.com/projectdiscovery/nuclei-templates/blob/main/cves/2024/CVE-2024-1234.yaml"
                            ],
                            "potential_github_pocs": [
                                "https://github.com/security-researcher/CVE-2024-1234-poc"
                            ]
                        },
                        "epss_score": {
                            "epss": 0.89234,
                            "percentile": 0.99876,
                            "date": "2024-01-21"
                        }
                    }
                ]
            }
        ],
        "statistics": {
            "total_cpes_analyzed": 2,
            "successful_analyses": 2,
            "failed_analyses": 0,
            "total_vulnerabilities": 5,
            "severity_breakdown": {
                "critical": 1,
                "high": 2,
                "medium": 1,
                "low": 1
            },
            "epss_statistics": {
                "total_with_epss": 4,
                "total_without_epss": 1,
                "coverage_percentage": 80.0,
                "high_risk_count": 2,
                "average_epss": 0.4092,
                "max_epss": 0.8923,
                "min_epss": 0.0023
            }
        }
    }
}
```

---

### Complete Request Flow - CPE-Based Analysis

**1. Receive API request with CPE parameter**
```http
GET /asm/stack-vulns?cpe=cpe:2.3:a:apache:http_server:2.4.41:*:*:*:*:*:*:*
```
↓

**2. Validate CPE format**
```python
# CPE validation checks:
# - Must start with "cpe:2.3:"
# - Must have exactly 13 colon-separated components
# - Basic format validation
```
↓

**3. Execute direct NVD query for the CPE**
```python
# Direct analysis without tech detection dependency
cpe_info = {
    "cpe": "cpe:2.3:a:apache:http_server:2.4.41:*:*:*:*:*:*:*",
    "host": "N/A",
    "tech_name": "N/A", 
    "tech_version": "N/A"
}
```
↓

**4. Follow same vulnerability analysis process**
- NVD API query
- CVE data processing  
- Exploit intelligence gathering
- Result aggregation

---

## API Reference

### GET /asm/stack-vulns

**Query Parameters:**
- `domain` (string, optional): Domain name to analyze
- `cpe` (string, optional): CPE string to analyze directly
- `job_id` (string, optional): Job ID to check status
- `ignore_cache` (boolean, optional): Force fresh analysis (default: false)

**Validation Rules:**
- Exactly one of `domain`, `cpe`, or `job_id` must be provided
- Domain format: Standard domain validation
- CPE format: Must follow CPE 2.3 specification (13 components)

**Request Examples:**
```bash
# Domain-based analysis
curl "https://api.threatmesh.com/asm/stack-vulns?domain=example.com"

# CPE-based analysis  
curl "https://api.threatmesh.com/asm/stack-vulns?cpe=cpe:2.3:a:wordpress:wordpress:6.8.1:*:*:*:*:*:*:*"

# Force fresh analysis
curl "https://api.threatmesh.com/asm/stack-vulns?domain=example.com&ignore_cache=true"

# Check job status
curl "https://api.threatmesh.com/asm/stack-vulns?job_id=stack-abc123-def456-ghi789"
```

### POST /asm/stack-vulns

**Request Body:**
```json
{
    "domain": "example.com"
}
```
or
```json
{
    "cpe": "cpe:2.3:a:apache:http_server:2.4.41:*:*:*:*:*:*:*"
}
```

**Query Parameters:** Same as GET endpoint

---

## Response Format

### Successful Response (200 OK)

**Pending Task:**
```json
{
    "job_id": "stack-abc123-def456-ghi789",
    "status": "pending",
    "feature": "stack_vulnerabilities",
    "message": "Task queued for processing",
    "progress_perc": 0
}
```

**Running Task:**
```json
{
    "job_id": "stack-abc123-def456-ghi789", 
    "status": "running",
    "feature": "stack_vulnerabilities",
    "message": "Analyzing CPE 1/2: cpe:2.3:a:wordpress:wordpress:6.8.1:*:*:*:*:*:*:*",
    "progress_perc": 45,
    "start_time": "2024-01-20T12:00:00Z"
}
```

**Completed Task:**
```json
{
    "job_id": "stack-abc123-def456-ghi789",
    "status": "completed", 
    "feature": "stack_vulnerabilities",
    "message": "Stack vulnerabilities analysis completed. Found 5 vulnerabilities across 2 CPEs.",
    "start_time": "2024-01-20T12:00:00Z",
    "end_time": "2024-01-20T12:03:45Z",
    "execution_time": 225,
    "progress_perc": 100,
    "cache_hit": false,
    "data": {
        "stack_vulnerabilities": [
            {
                "cpe": "cpe:2.3:a:wordpress:wordpress:6.8.1:*:*:*:*:*:*:*",
                "host": "blog.example.com",
                "tech_name": "WordPress",
                "tech_version": "6.8.1", 
                "vulnerability_count": 3,
                "vulnerabilities": [
                    {
                        "cve_id": "CVE-2024-1234",
                        "description": "WordPress Core is vulnerable to SQL Injection in the comment functionality due to insufficient escaping on the user supplied parameter and lack of sufficient preparation on the existing SQL query.",
                        "severity": "CRITICAL",
                        "weakness(CWE-ID)": "CWE-89",
                        "published": "2024-01-15T10:15:00.000",
                        "lastModified": "2024-01-20T14:30:00.000",
                        "KEV": true,
                        "metrics": {
                            "cvssMetricV31": [
                                {
                                    "type": "Primary",
                                    "cvssData": {
                                        "version": "3.1",
                                        "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H",
                                        "baseScore": 9.8,
                                        "baseSeverity": "CRITICAL"
                                    }
                                }
                            ]
                        },
                        "nvd_link": "https://nvd.nist.gov/vuln/detail/CVE-2024-1234",
                        "public_exploits": {
                            "exploitDB": [
                                "https://www.exploit-db.com/exploits/51234"
                            ],
                            "nuclei_templates": [
                                "https://github.com/projectdiscovery/nuclei-templates/blob/main/cves/2024/CVE-2024-1234.yaml"
                            ],
                            "potential_github_pocs": [
                                "https://github.com/security-researcher/CVE-2024-1234-poc",
                                "https://github.com/exploit-dev/wordpress-sqli-poc"
                            ]
                        },
                        "epss_score": {
                            "epss": 0.89234,
                            "percentile": 0.99876,
                            "date": "2024-01-21"
                        }
                    }
                ]
            }
        ],
        "statistics": {
            "total_cpes_analyzed": 2,
            "successful_analyses": 2,
            "failed_analyses": 0,
            "total_vulnerabilities": 5,
            "severity_breakdown": {
                "critical": 1,
                "high": 2,
                "medium": 1,
                "low": 1
            },
            "epss_statistics": {
                "total_with_epss": 4,
                "total_without_epss": 1,
                "coverage_percentage": 80.0,
                "high_risk_count": 2,
                "average_epss": 0.4092,
                "max_epss": 0.8923,
                "min_epss": 0.0023
            }
        }
    }
}
```

### Error Responses

**400 Bad Request:**
```json
{
    "detail": "Either 'domain' or 'cpe' parameter must be provided"
}
```

**404 Not Found:**
```json
{
    "detail": "Job stack-abc123-def456-ghi789 not found"
}
```

**500 Internal Server Error:**
```json
{
    "detail": "Error starting task: NVD API temporarily unavailable"
}
```

---

## Data Fields Reference

### Stack Vulnerability Object
- `cpe` (string): Common Platform Enumeration identifier
- `host` (string): Host/subdomain where technology was detected
- `tech_name` (string): Technology name (e.g., "WordPress", "Apache")
- `tech_version` (string): Technology version (e.g., "6.8.1", "2.4.41")
- `vulnerability_count` (integer): Number of vulnerabilities found
- `vulnerabilities` (array): Array of CVE objects

### CVE Object
- `cve_id` (string): CVE identifier (e.g., "CVE-2024-1234")
- `description` (string): English description of the vulnerability
- `severity` (string): CVSS severity rating (CRITICAL, HIGH, MEDIUM, LOW)
- `weakness(CWE-ID)` (string): Common Weakness Enumeration identifier
- `published` (string): Publication date (ISO 8601 format)
- `lastModified` (string): Last modification date (ISO 8601 format)
- `KEV` (boolean): Whether CVE is in CISA Known Exploited Vulnerabilities catalog
- `metrics` (object): Complete CVSS metrics from NVD
- `nvd_link` (string): Direct link to NVD page
- `public_exploits` (object): Exploit intelligence from multiple sources
- `epss_score` (object): EPSS exploitation probability data from FIRST.org

### Public Exploits Object
- `exploitDB` (array): Links to ExploitDB exploit entries
- `nuclei_templates` (array): Links to Nuclei vulnerability templates
- `potential_github_pocs` (array): Links to potential GitHub proof-of-concept code

### EPSS Score Object
- `epss` (float): Exploitation probability score (0.0 - 1.0, higher = more likely to be exploited)
- `percentile` (float): Percentile ranking among all CVEs (0.0 - 1.0)
- `date` (string): Date of EPSS data (YYYY-MM-DD format)
- `error` (string, optional): Error message if EPSS data unavailable

### Statistics Object
- `total_cpes_analyzed` (integer): Number of CPEs processed
- `successful_analyses` (integer): Number of successful vulnerability analyses
- `failed_analyses` (integer): Number of failed analyses
- `total_vulnerabilities` (integer): Total CVEs found across all CPEs
- `severity_breakdown` (object): Count of vulnerabilities by severity level
- `epss_statistics` (object): EPSS coverage and exploitation risk statistics

### EPSS Statistics Object
- `total_with_epss` (integer): Number of CVEs with EPSS scores
- `total_without_epss` (integer): Number of CVEs without EPSS scores
- `coverage_percentage` (float): Percentage of CVEs with EPSS scores
- `high_risk_count` (integer): Number of CVEs with EPSS score > 0.5 (high exploitation probability)
- `average_epss` (float): Average EPSS score across all CVEs with scores
- `max_epss` (float): Highest EPSS score found
- `min_epss` (float): Lowest EPSS score found

---

## Integration & Dependencies

### Required Previous Steps
**For Domain-based Analysis:**
1. **Technology Detection**: Must run tech detection first to get CPE data
2. **Data Dependency**: Requires recent (within 30 days) tech detection results
3. **CPE Requirements**: Technologies must have valid CPE identifiers and versions

### External API Dependencies
1. **NVD CVE API 2.0**: Primary vulnerability data source
   - Rate Limits: 5 requests/30 seconds (public), 50 requests/30 seconds (with API key)
   - Data Updates: CVE data updated continuously
   - Authentication: Optional API key for higher rate limits

2. **FIRST.org EPSS API**: Exploit Prediction Scoring System data
   - Rate Limits: 1000 requests/minute (public)
   - Data Updates: EPSS scores updated daily
   - Batch Support: Up to 50 CVEs per request (2000 character URL limit)
   - Endpoint: https://api.first.org/data/v1/epss

3. **ExploitDB CSV**: Exploit database search
   - Source: GitLab exploit-database repository
   - Update Frequency: Updated regularly
   - Format: CSV with exploit metadata

4. **Trickest CVE Database**: Community-driven exploit intelligence
   - Source: GitHub trickest/cve repository
   - Content: Curated exploit links and POCs
   - Format: Markdown files per CVE

5. **Nuclei Templates**: Vulnerability scanning templates
   - Source: ProjectDiscovery nuclei-templates repository
   - Format: JSONL database of template metadata
   - Coverage: Active vulnerability scanning templates

### Performance Considerations
- **Caching**: Results cached for 30 days
- **Rate Limiting**: Built-in retry logic with exponential backoff
- **Parallel Processing**: CPEs analyzed concurrently where possible
- **Memory Management**: Raw outputs cleared after processing
- **Timeout Handling**: 60-second timeout per API request

---

## Use Cases

### Security Assessment
- **Vulnerability Discovery**: Identify known vulnerabilities in detected technologies
- **Risk Prioritization**: Focus on CRITICAL/HIGH severity vulnerabilities with public exploits
- **EPSS-Based Prioritization**: Prioritize vulnerabilities by exploitation probability (EPSS > 0.5)
- **KEV Monitoring**: Track vulnerabilities actively exploited in the wild
- **Compliance**: Meet vulnerability management requirements

### Penetration Testing
- **Attack Surface**: Map exploitable vulnerabilities in target infrastructure
- **Exploit Research**: Find public POCs and exploitation tools
- **Template Usage**: Leverage Nuclei templates for automated testing
- **GitHub Intelligence**: Discover community exploit code

### Continuous Monitoring
- **Stack Tracking**: Monitor vulnerability status of technology stack
- **Alert Generation**: Set up alerts for new critical vulnerabilities
- **Trend Analysis**: Track vulnerability trends over time
- **Integration**: Feed data into SIEM/SOAR platforms

---

## Best Practices

### API Usage
1. **Domain Analysis**: Always run tech detection first for comprehensive results
2. **CPE Analysis**: Use when analyzing specific technologies directly
3. **Cache Management**: Use `ignore_cache=true` only when fresh data is required
4. **Rate Limiting**: Implement client-side rate limiting for bulk operations

### Security Considerations
1. **Prioritization**: Focus on KEV-listed vulnerabilities first
2. **EPSS-Informed Decisions**: Use EPSS scores to prioritize based on exploitation likelihood
   - **High Priority**: EPSS > 0.7 (Top 10% exploitation probability)
   - **Medium Priority**: EPSS 0.3-0.7 (Moderate exploitation probability)
   - **Low Priority**: EPSS < 0.3 (Lower exploitation probability)
3. **Combined Assessment**: Consider CVSS severity, EPSS probability, and exploit availability together
4. **Context Awareness**: Evaluate vulnerabilities based on exposure and criticality
5. **Regular Updates**: Re-run analysis regularly as new vulnerabilities are discovered and EPSS scores update

### EPSS Score Interpretation
- **EPSS 0.9-1.0**: Extremely high probability of exploitation (Top 1%)
- **EPSS 0.7-0.9**: Very high probability of exploitation (Top 10%)
- **EPSS 0.5-0.7**: High probability of exploitation (Top 25%)
- **EPSS 0.3-0.5**: Moderate probability of exploitation
- **EPSS 0.1-0.3**: Low probability of exploitation
- **EPSS 0.0-0.1**: Very low probability of exploitation

### Performance Optimization
1. **Batch Processing**: Group related domains/CPEs for efficient processing
2. **Monitoring**: Track job progress using job_id status checks
3. **Error Handling**: Implement retry logic for transient failures
4. **Resource Management**: Consider API rate limits and processing time

---

## Error Handling

### Common Error Scenarios
1. **No Tech Detection Data**: Domain analysis requires existing tech detection results
2. **Invalid CPE Format**: CPE must follow exact specification format
3. **NVD API Unavailable**: Temporary API outages or rate limit exceeded
4. **EPSS API Issues**: Rate limiting or temporary unavailability of EPSS data
5. **Network Timeouts**: External service connectivity issues

### Resolution Strategies
1. **Tech Detection Dependency**: Run tech detection first before vulnerability analysis
2. **CPE Validation**: Verify CPE format using official CPE tools
3. **Retry Logic**: Implement exponential backoff for transient failures
4. **Fallback Strategies**: Cache previous results for continuity

---

## Support & Troubleshooting

### Common Issues
- **No vulnerabilities found**: Technology may not have known vulnerabilities or CPE mapping issues
- **Rate limiting**: NVD API rate limits exceeded - use API key or implement delays
- **Missing EPSS scores**: Some CVEs may not have EPSS data available (especially older CVEs)
- **Incomplete results**: Some exploit sources may be temporarily unavailable
- **Tech detection required**: Domain analysis requires previous tech detection results

### Debug Information
- Check tech detection results for valid CPE data
- Verify NVD API connectivity and rate limits
- Monitor external service availability
- Review job logs for specific error details