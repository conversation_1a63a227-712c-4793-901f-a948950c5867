# Working

## Data Flow Explanation

### Complete Request Flow

**1. Receive API request with domain + parameters**
```http
GET /cti/phishing_domains?domain=example.com&ignore_cache=false
```
↓

**2. Validate request parameters (domain, job_id, ignore_cache)**
```python
# Validated parameters
{
    "domain": "example.com",
    "job_id": None,
    "ignore_cache": False,
    "detailed": False  # Not used for phishing domains
}
```
↓

**3. Check for existing job_id or create new task**
```python
# If job_id exists, return cached result
# If new request, generate new job_id
job_id = "e5f6g7h8-i9j0-1234-efgh-i56789012345"
```
↓

**4. Create Celery background task job**
```python
task = run_phishing_domain_scan.delay(
    domain="example.com",
)
```
↓

**5. Initialize dnstwist with custom dictionaries:**
- Tool: dnstwist library (domain permutation engine)
- Custom dictionaries: 28 terms + 22 TLD variants
- Fuzzing algorithms: 16 different permutation techniques
- Cache expiration: 12 hours

```python
# Custom dictionaries for enhanced detection
DICTIONARY = ('auth', 'account', 'confirm', 'connect', 'enroll', 'http', 'https', 
             'info', 'login', 'mail', 'my', 'online', 'payment', 'portal', 
             'recovery', 'register', 'ssl', 'safe', 'secure', 'signin', 
             'signup', 'support', 'update', 'user', 'verify', 'verification', 
             'web', 'www')

TLD_DICTIONARY = ('com', 'net', 'org', 'info', 'cn', 'co', 'eu', 'de', 'uk', 
                 'pw', 'ga', 'gq', 'tk', 'ml', 'cf', 'app', 'biz', 'top', 
                 'xyz', 'online', 'site', 'live')
```
↓

**6. dnstwist executes domain permutation generation:**
- Generates variants using 16 fuzzing algorithms
- Creates thousands of potential phishing domains
- Applies custom dictionary terms for targeted variants
- Uses TLD dictionary for different extensions

```python
# Sample domain permutations generated
[
    {"fuzzer": "*original", "domain": "example.com"},
    {"fuzzer": "addition", "domain": "examplel.com"},
    {"fuzzer": "bitsquatting", "domain": "exaople.com"},
    {"fuzzer": "dictionary", "domain": "loginexample.com"},
    {"fuzzer": "homoglyph", "domain": "examp1e.com"},
    {"fuzzer": "hyphenation", "domain": "ex-ample.com"},
    {"fuzzer": "insertion", "domain": "exapmple.com"},
    {"fuzzer": "omission", "domain": "examle.com"},
    {"fuzzer": "replacement", "domain": "examole.com"},
    {"fuzzer": "subdomain", "domain": "ex.ample.com"},
    {"fuzzer": "tld-swap", "domain": "example.net"},
    {"fuzzer": "transposition", "domain": "examlpe.com"},
    {"fuzzer": "various", "domain": "example.org"},
    {"fuzzer": "vowel-swap", "domain": "exomple.com"}
]
```
↓

**7. DNS resolution and enrichment for registered domains:**
- Check registration status for generated permutations
- Resolve DNS records (A, AAAA, NS, MX, CNAME, TXT)
- Filter to only registered/active domains
- Collect additional threat intelligence data

```python
# Sample registered domain with DNS data
{
    "fuzzer": "addition",
    "domain": "examplel.com",
    "dns_a": ["************"],
    "dns_ns": ["ns1.malicious-host.com", "ns2.malicious-host.com"],
    "dns_mx": ["mail.examplel.com"],
    "dns_txt": ["v=spf1 include:_spf.google.com ~all"]
}
```
↓

**8. Process and analyze results:**
- Filter registered domains from all permutations
- Generate statistics by fuzzing algorithm
- Analyze DNS resolution patterns
- Create threat intelligence summaries

```python
# Database storage structure
{
    "job_id": "e5f6g7h8-i9j0-1234-efgh-i56789012345",
    "status": "COMPLETED",
    "structured_output": {
        "registered_domains": [
            {
                "fuzzer": "addition",
                "domain": "examplel.com",
                "dns_a": ["************"],
                "dns_ns": ["ns1.malicious-host.com"],
                "dns_mx": ["mail.examplel.com"]
            }
        ],
        "statistics": {
            "total_domains_generated": 2156,
            "registered_count": 11,
            "unregistered_count": 2145,
            "fuzzing_algorithms_used": ["*original", "addition", "bitsquatting", "dictionary", "homoglyph"],
            "registered_by_fuzzer": {
                "*original": 1,
                "addition": 2,
                "bitsquatting": 1,
                "omission": 3,
                "replacement": 2
            },
            "dns_resolution_stats": {
                "resolved_domains": 11,
                "a_records": 11,
                "aaaa_records": 1,
                "ns_records": 10,
                "mx_records": 6
            }
        }
    }
}
```
↓

**9. Return response to client:**
- If task pending: return job_id + status
- If completed: return registered domains + comprehensive statistics
- Support cache_hit for repeated requests

```json
{
    "job_id": "e5f6g7h8-i9j0-1234-efgh-i56789012345",
    "status": "completed",
    "feature": "phishing_domains",
    "start_time": "2024-01-20T12:00:00Z",
    "end_time": "2024-01-20T12:04:30Z",
    "execution_time": 270,
    "results": {
        "registered_domains": [
            {
                "fuzzer": "*original",
                "domain": "example.com",
                "dns_a": ["************"],
                "dns_ns": ["ns1.example.com"],
                "dns_mx": ["mail.example.com"]
            },
            {
                "fuzzer": "addition",
                "domain": "examplel.com",
                "dns_a": ["************"],
                "dns_ns": ["ns1.malicious-host.com"],
                "dns_mx": ["mail.examplel.com"]
            },
            {
                "fuzzer": "bitsquatting",
                "domain": "exaople.com",
                "dns_a": ["************"],
                "dns_ns": ["ns1.suspicious-provider.org"]
            }
        ],
        "statistics": {
            "total_domains_generated": 2156,
            "registered_count": 11,
            "unregistered_count": 2145,
            "fuzzing_algorithms_used": [
                "*original", "addition", "bitsquatting", "dictionary", 
                "homoglyph", "hyphenation", "insertion", "omission", 
                "replacement", "subdomain", "tld-swap", "transposition", 
                "various", "vowel-swap"
            ],
            "registered_by_fuzzer": {
                "*original": 1,
                "addition": 2,
                "bitsquatting": 1,
                "omission": 3,
                "replacement": 2,
                "subdomain": 1,
                "various": 1
            },
            "dns_resolution_stats": {
                "resolved_domains": 11,
                "a_records": 11,
                "aaaa_records": 1,
                "ns_records": 10,
                "mx_records": 6,
                "cname_records": 0,
                "txt_records": 0
            },
            "dictionaries_used": {
                "custom_dictionary_terms": 28,
                "tld_dictionary_terms": 22
            }
        }
    },
    "cache_hit": false
}
```

### Key Components Interaction

**API Layer** (`app/routers/cti/phishing_domains.py`)
- Handles HTTP requests for `/cti/phishing_domains`
- Validates domain parameters
- Manages task lifecycle and caching
- Returns formatted responses

**Task Layer** (`app/tasks/cti/phishing_domains.py`) 
- Executes dnstwist permutation generation
- Performs DNS resolution for generated domains
- Processes and filters registered domains
- Generates comprehensive statistics

**Permutation Engine** (`dnstwist` library)
- Official dnstwist library for domain permutations
- 16 different fuzzing algorithms
- Custom dictionary support for enhanced detection
- DNS resolution capabilities

**Data Storage**
- MongoDB for result persistence
- Registered domains with full DNS records
- Comprehensive statistics and analytics
- Cache expiration: 12 hours (configurable)

### Fuzzing Algorithms

**Character-based Algorithms:**
- **Addition**: Adds characters to domain name
- **Omission**: Removes characters from domain name
- **Replacement**: Replaces characters with similar ones
- **Transposition**: Swaps adjacent characters
- **Insertion**: Inserts characters based on keyboard layout

**Advanced Algorithms:**
- **Bitsquatting**: Flips bits in characters (e.g., 'a' → 'c')
- **Homoglyph**: Uses visually similar characters (e.g., 'o' → '0')
- **Vowel-swap**: Replaces vowels with other vowels
- **Dictionary**: Uses predefined terms to create variants
- **Hyphenation**: Inserts hyphens into domain name

**Structure-based Algorithms:**
- **Subdomain**: Creates subdomain variants
- **TLD-swap**: Changes top-level domain
- **Various**: Combines multiple techniques

### Custom Dictionaries

**Authentication/Security Terms:**
```python
DICTIONARY = ('auth', 'account', 'confirm', 'connect', 'enroll', 'login', 
             'recovery', 'register', 'secure', 'signin', 'signup', 
             'verify', 'verification')
```

**Web/Service Terms:**
```python
DICTIONARY += ('http', 'https', 'info', 'mail', 'my', 'online', 'payment', 
              'portal', 'ssl', 'safe', 'support', 'update', 'user', 'web', 'www')
```

**Malicious TLD Extensions:**
```python
TLD_DICTIONARY = ('com', 'net', 'org', 'info', 'cn', 'co', 'eu', 'de', 'uk', 
                 'pw', 'ga', 'gq', 'tk', 'ml', 'cf', 'app', 'biz', 'top', 
                 'xyz', 'online', 'site', 'live')
```

### DNS Resolution Analysis

**Collected DNS Records:**
- **A Records**: IPv4 addresses of the domain
- **AAAA Records**: IPv6 addresses of the domain
- **NS Records**: Name server information
- **MX Records**: Mail exchange servers
- **CNAME Records**: Canonical name aliases
- **TXT Records**: Text records (SPF, DKIM, etc.)

**Threat Intelligence Indicators:**
- Suspicious hosting providers
- Shared infrastructure patterns
- Certificate transparency logs
- Domain registration patterns

### Cache Management

**Cache Duration: 12 hours**
```python
# Cache configuration
TASK_EXPIRATION = {
    "phishing_domains": timedelta(hours=12)
}

# Cache behavior
- Running tasks: Always return existing task
- Completed tasks: Return cached results within 12 hours
- Expired cache: Start new permutation analysis
- ignore_cache=true: Force new scan regardless of cache
```

**Medium Cache Benefits:**
- Domain registrations don't change frequently
- DNS propagation takes time
- Reduces computational overhead
- Balance between freshness and resource usage

### Security Considerations

**Phishing Indicators:**
- Domains using authentication-related terms
- Recently registered domains
- Shared hosting infrastructure
- Suspicious TLD choices
- Certificate inconsistencies

**Detection Patterns:**
- Multiple domains on same IP
- Similar DNS configurations
- Fast-flux hosting patterns
- Domain parking services
- Suspicious registrar patterns 