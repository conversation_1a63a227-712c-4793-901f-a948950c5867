# Working

## Data Flow Explanation

### Complete Request Flow

**1. Receive API request with domain + parameters**
```http
GET /pastes_search?domain=example.com&secrets=true&cse_page_limit=10&ignore_cache=false
```
↓

**2. Validate request parameters (domain, secrets, cse_page_limit, pagination)**
```python
# Validated parameters
{
    "domain": "example.com",
    "secrets": True,  # Whether to analyze paste content for secrets
    "cse_page_limit": 10,  # Max 100 CSE pages to fetch
    "ignore_cache": False,
    "page": 1,
    "records_per_page": 100
}
```
↓

**3. Check for existing job_id or create new task**
```python
# If job_id exists, return cached result
# If new request, generate new job_id
job_id = "b2c3d4e5-f6g7-8901-bcde-f23456789012"
```
↓

**4. Create Celery background task job**
```python
task = run_pastes_search_by_domain.delay(
    domain="example.com",
    job_id="b2c3d4e5-f6g7-8901-bcde-f23456789012",
    analyze_secrets=True,
    chunk_size=100,
    cse_page_limit=10
)
```
↓

**5. Execute Google CSE API pagination calls:**
- Current: Single CSE engine (e7ea81b606417454c) with configured paste sites
- Future: 4 CSE engines with 10 sites each for max coverage
- API Cost: 100 free queries/day, then $5 per 1,000 queries
- CSE pages: Up to 10 pages max (100 results total from Google CSE)

```python
# CSE API call with pagination
cse_params = {
    "key": GOOGLE_CSE_API_KEY,
    "cx": "e7ea81b606417454c",  # CSE Engine ID
    "exactTerms": "example.com",  # Search for exact domain
    "sort": "date",  # Sort by date
    "start": 1,  # Starting index (1, 11, 21, 31, etc.)
    "num": 10   # Results per request (Google CSE returns max 10)
}

# Loop through multiple pages up to cse_page_limit
# start_index = 1, 11, 21, 31, 41, 51, 61, 71, 81, 91 (max 10 pages)
```
↓

**6. Google CSE executes search across configured paste sites:**
- Searches all paste sites in CSE for exact domain term
- Returns 10 results per page, up to 10 pages max
- Each API call = 1 query (regardless of results returned)

```json
# Sample CSE API response for start=1
{
  "kind": "customsearch#search",
  "searchInformation": {
    "totalResults": "87"
  },
  "items": [
    {
      "title": "Database backup with credentials - Pastebin.com",
      "link": "https://pastebin.com/ABC123XY",
      "snippet": "mysql://admin:<EMAIL>:3306/database config file"
    },
    {
      "title": "Configuration file - JustPaste.it",
      "link": "https://justpaste.it/DEF456UV",
      "snippet": "API endpoints for example.com production environment"
    }
  ]
}
```
↓

**7. Parse and process CSE results + optional secret analysis:**
- Extract basic paste information (title, link, snippet)
- If secrets=true: Convert URLs to raw format and fetch content
- Analyze both snippet and raw content for secrets using SecretDetection
- Track paste sources and metadata

**Raw URL Conversion Process (when secrets=true):**
```python
# URL conversion mappings for major paste sites
url_conversions = {
    "https://pastebin.com/ABC123XY" → "https://pastebin.com/raw/ABC123XY",
    "https://pastebin.pl/view/12345" → "https://pastebin.pl/view/raw/12345", 
    "https://paste.ee/p/67890" → "https://paste.ee/r/67890",
    "https://dpaste.org/ABCDEF" → "https://dpaste.org/ABCDEF/raw",
    "https://gist.github.com/user/abc123" → "https://gist.github.com/user/abc123/raw",
    "https://ideone.com/fork/XYZ789" → "https://ideone.com/plain/XYZ789"
    # 20+ paste site mappings available
}

# If no mapping exists, returns False (skip content fetching)
# If mapping exists, fetch raw content for secret analysis
```

**Processing Steps:**
```python
# Basic processed result structure
{
    "title": "Database backup with credentials",
    "link": "https://pastebin.com/ABC123XY",
    "snippet": "mysql://admin:<EMAIL>:3306/database config file"
}

# If secrets=true, enhanced with secret analysis
{
    "title": "Database backup with credentials", 
    "link": "https://pastebin.com/ABC123XY",
    "snippet": "mysql://admin:<EMAIL>:3306/database config file",
    "secrets": [
        {
            "type": "mysql_connection_string",
            "value": "mysql://admin:<EMAIL>:3306/database",
            "source": "search_snippet"  # Found in CSE snippet
        },
        {
            "type": "database_password", 
            "value": "password123",
            "source": "raw_file_content"  # Found in fetched raw content
        }
    ]
}
```
↓

**8. Store results in database chunks:**
- Store results in chunks of 100 records each
- Generate summary statistics with paste sources
- Track secret analysis metrics if enabled
- Store pagination metadata for large result sets

```python
# Database chunk storage structure
{
    "operation_id": "b2c3d4e5-f6g7-8901-bcde-f23456789012",
    "domain": "example.com",
    "chunk_number": 0,
    "created_at": "2024-01-20T12:00:00Z",
    "count": 87,
    "data": [/* 87 paste records */]
}

# Summary stored in operations collection
{
    "job_id": "b2c3d4e5-f6g7-8901-bcde-f23456789012",
    "status": "COMPLETED",
    "structured_output": {
        "summary": {
            "total_results": 87,
            "total_chunks": 1,
            "chunk_size": 100,
            "paste_sources": ["pastebin.com", "justpaste.it", "rentry.co"],
            "unique_sources_count": 3,
            "domain_searched": "example.com",
            "secrets_analysis_enabled": True,
            "total_pastes_analyzed": 45,  # How many had fetchable content
            "secrets_found": 23,  # Total secrets detected
            "pastes_with_secrets": 12  # Pastes containing secrets
        }
    }
}
```
↓

**9. Return response to client:**
- If task pending: return job_id + status
- If completed: return paginated results + summary
- Support cache_hit for repeated requests
- Include secret analysis statistics if enabled

```json
{
    "job_id": "b2c3d4e5-f6g7-8901-bcde-f23456789012",
    "status": "COMPLETED",
    "feature": "pastes_search",
    "start_time": "2024-01-20T12:00:00Z",
    "end_time": "2024-01-20T12:03:45Z",
    "execution_time": 225,
    "results": {
        "summary": {
            "total_results": 87,
            "total_chunks": 1,
            "chunk_size": 100,
            "paste_sources": ["pastebin.com", "justpaste.it", "rentry.co"],
            "unique_sources_count": 3,
            "domain_searched": "example.com",
            "secrets_analysis_enabled": true,
            "total_pastes_analyzed": 45,
            "secrets_found": 23,
            "pastes_with_secrets": 12
        },
        "records": [
            {
                "title": "Database backup with credentials",
                "link": "https://pastebin.com/ABC123XY",
                "snippet": "mysql://admin:<EMAIL>:3306/database config file",
                "secrets": [
                    {
                        "type": "mysql_connection_string",
                        "value": "mysql://admin:<EMAIL>:3306/database",
                        "source": "search_snippet"
                    }
                ]
            }
        ],
        "pagination": {
            "total_records": 87,
            "page": 1,
            "total_pages": 1,
            "records_per_page": 100
        }
    }
}
```

### Key Components Interaction

**API Layer** (`app/routers/cti/paste_search.py`)
- Handles HTTP requests
- Validates parameters and manages quotas
- Tracks API usage and costs
- Returns formatted responses

**Task Layer** (`app/tasks/cti/paste_search.py`) 
- Executes Google CSE API calls
- Processes and filters results
- Handles URL validation and conversion
- Updates task status and usage metrics

**CSE Configuration**
- Current: Single CSE engine (e7ea81b606417454c)
- Future: 4 CSE engines for better coverage
- Cost optimization: 100 free queries/day, $5/1000 after
- Daily limits: 10,000 queries max per project

**Data Storage**
- MongoDB for result persistence
- API usage tracking and cost monitoring
- Duplicate URL detection and removal
- Pagination support for large result sets

### API Cost Management

**Free Tier (100 queries/day)**
```python
# Cost tracking
daily_usage = {
    "free_queries_used": 24,
    "free_queries_remaining": 76,
    "cost_today": 0.00
}
```

**Paid Tier ($5 per 1,000 queries)**
```python
# Cost calculation after free tier
if queries_used > 100:
    paid_queries = queries_used - 100
    cost = (paid_queries / 1000) * 5.00
    
# Example: 1,500 queries in a day
# Free: 100 queries = $0.00
# Paid: 1,400 queries = $7.00
# Total cost: $7.00
``` 

# Paste Search Sources paste site & Future Improvement

Pastes Sites REPO - https://github.com/lorien/awesome-pastebins



TOP TRAFIIC PASTES COLLECTED FROM - https://pro.similarweb.com/#/digitalsuite/websiteanalysis/overview/website-performance/-/999/1m?webSource=Total&key=pastebin.com

FLAGGED THE ONES USED IN REDHUNT - https://redhuntlabs.com/online-ide-search/
```bash
pastebin.com - 8.12M - redhunt
justpaste.it - 7.20M - redhunt
pastelink.net - 7.03M
rentry.co - 5.54M
codepen.io - 5.13M - redhunt
rentry.org - 2.56M
anotepad.com - 2.15M
codebeautify.org - 1.60M
jsfiddle.net - 1.02M - redhunt
controlc.com - 618.60K
codeshare.io - 397.32K
pastebin.pl - 287.02K
notes.io - 284.76K
etextpad.com - 253.51K
ideone.com - 198.52K - redhunt
dotnetfiddle.net - 181.29K - redhunt
jsbin.com - 173.90K - redhunt
paste2.org - 172.89K - redhunt
paste.ee - 144.22K
pasteyou.com - 70.23K
ide.geeksforgeeks.org - 73.38K - redhunt
klipit.in - 56.93K
ivpaste.com - 57.68K
justpaste.me - 57.17K
pastecode.io - 57.14K
cl1p.net - 52.36K
privatebin.info - 45.81K
hastebin.com - 47.55K - redhunt
defuse.ca - 47.20K
dpaste.com - 35.03K - redhunt
jsitor.com - 25.55K - redhunt
dpaste.org - 23.30K - redhunt
p.ip.fi - 10.63K
pastebin.in - 12.92K
pastesio.com - 8.15K
paste.sh - 7.57K
snippet.host - 7.46K
tutpaste.com - 5.97K
0bin.net - 5.58K
paste.debian.net - 5.68K - redhunt
paste.opensuse.org - 5.43K - redhunt
paste.centos.org - 4.51K
paste.ofcode.org - 3.71K
codebunk.com - 3.60K
paste-bin.xyz - 3.66K
termbin.com - 2.23K
bitbin.it - 2.14K
bpa.st - 1.76K
pastejustit.com - 1.29K
paste.myst.rs - 938.52
paste.rohitab.com - 342.51
paste.jp - 309.60
cutapaste.net - 223.40
paste.js.org - 116.09
```


```bash
pastebin.com/*
justpaste.it/*
pastelink.net/*
rentry.co/*
codepen.io/*
rentry.org/*
anotepad.com/*
codebeautify.org/*
jsfiddle.net/*
controlc.com/*
codeshare.io/*
pastebin.pl/*
notes.io/*
etextpad.com/*
ideone.com/*
dotnetfiddle.net/*
jsbin.com/*
paste2.org/*
paste.ee/*
pasteyou.com/*
ide.geeksforgeeks.org/*
klipit.in/*
ivpaste.com/*
justpaste.me/*
pastecode.io/*
cl1p.net/*
privatebin.info/*
hastebin.com/*
defuse.ca/*
dpaste.com/*
jsitor.com/*
dpaste.org/*
p.ip.fi/*
pastebin.in/*
pastesio.com/*
paste.sh/*
snippet.host/*
tutpaste.com/*
0bin.net/*
paste.debian.net/*
paste.opensuse.org/*
paste.centos.org/*
paste.ofcode.org/*
codebunk.com/*
paste-bin.xyz/*
termbin.com/*
bitbin.it/*
bpa.st/*
pastejustit.com/*
paste.myst.rs/*
paste.rohitab.com/*
paste.jp/*
cutapaste.net/*
paste.js.org/*
```

# Current Implementation & Future Improvement 
## Current Implementation 
Single CSE engine below top 20 : THE LESSER SITES IN CSE MORE BETTER RESULTS
```bash
pastebin.com - 8.12M - redhunt
justpaste.it - 7.20M - redhunt
pastelink.net - 7.03M
rentry.co - 5.54M
codepen.io - 5.13M - redhunt
rentry.org - 2.56M
anotepad.com - 2.15M
codebeautify.org - 1.60M
jsfiddle.net - 1.02M - redhunt
controlc.com - 618.60K
codeshare.io - 397.32K
pastebin.pl - 287.02K
notes.io - 284.76K
etextpad.com - 253.51K
ideone.com - 198.52K - redhunt
dotnetfiddle.net - 181.29K - redhunt
jsbin.com - 173.90K - redhunt
paste2.org - 172.89K - redhunt
paste.ee - 144.22K
pasteyou.com - 70.23K
```
## Future create 4 CSE engines with 10 paste sites in each to get max coverage

## CSE ENGINE USED
https://programmablesearchengine.google.com/controlpanel/overview?cx=e7ea81b606417454c


## IMPROVEMENTS
- REMOVAL OF Not Exist Pastes shown in results of CSE ENGINE
- ALL PASTES SITES TO ADD VIA 4 CSE Engines
- Regexes Improvement
- Coverstion of paste url to raw url for secret search