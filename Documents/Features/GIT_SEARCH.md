# Working

## Data Flow Explanation

### Complete Request Flow

**1. Receive API request with identifiers + parameters**
```http
GET /git_search?identifiers=["example.com","@example.com"]&git_depth=20&ignore_cache=false
```
↓

**2. Validate request parameters (identifiers, git_depth, pagination)**
```python
# Validated parameters
{
    "identifiers": ["example.com", "@example.com"],
    "git_depth": 20,  # Within 1-50 range
    "ignore_cache": False,
    "page": 1,
    "records_per_page": 100
}
```
↓

**3. Check for existing job_id or create new task**
```python
# If job_id exists, return cached result
# If new request, generate new job_id
job_id = "a1b2c3d4-e5f6-7890-abcd-ef1234567890"
```
↓

**4. Create Celery background task job**
```python
task = git_search_task.delay(
    identifiers=["example.com", "@example.com"],
    git_depth=20
)
```
↓

**5. Pass parameters to git-hound command:**
- identifiers: JSON array converted to search terms
- git_depth: Number of pages to search (1-50)
- threads: 100 (fixed)
- timeout: 3600 seconds
- rules_dir: app/data/cti/git_search/githound-rules/ #1756 rules 
- output_format: JSONL

```bash
git-hound \
  --searchterms "example.com" "@example.com" \
  --pages 20 \
  --threads 100 \
  --timeout 3600 \
  --rules-dir app/data/cti/git_search/githound-rules/ \
  --output-format jsonl \
  --github-tokens $GIT_TOKENS
```
↓

**6. Git-hound executes search:**
- Uses GitHub API tokens for authentication
- Searches repositories based on identifiers
- Applies detection rules from YAML files
- Generates JSONL output

```json
# Sample raw JSONL output from git-hound
{"repo":"https://github.com/user/project","url":"https://github.com/user/project/blob/main/config.py","match":"AKIAIOSFODNN7EXAMPLE","context":"AWS_ACCESS_KEY_ID = \"AKIAIOSFODNN7EXAMPLE\"","rule":"aws-access-key","file_last_author":"developer","file_last_updated":"2024-01-15T10:30:00Z"}
{"repo":"https://github.com/company/api","url":"https://github.com/company/api/blob/master/.env","match":"sk-1234567890abcdef","context":"OPENAI_API_KEY=sk-1234567890abcdef","rule":"openai-api-key","file_last_author":"admin","file_last_updated":"2024-01-10T14:20:00Z"}
```
↓

**7. Parse and process raw results:**
- Filter valid JSON objects from JSONL
- Extract structured data fields
- Add processing timestamps
- Enrich with repository metadata

```python
# Processed result structure
{
    "attributes": ["aws-access-key", "AWS Access Key"],
    "context": "AWS_ACCESS_KEY_ID = \"AKIAIOSFODNN7EXAMPLE\"",
    "match": "AKIAIOSFODNN7EXAMPLE",
    "repo": "https://github.com/user/project",
    "url": "https://github.com/user/project/blob/main/config.py",
    "file_last_author": "developer",
    "file_last_updated": "2024-01-15T10:30:00Z",
    "processed_at": "2024-01-20T12:00:00Z",
    "rule_id": "aws-access-key",
    "rule_description": "AWS Access Key",
    "repo_owner": "user",
    "repo_name": "project"
}
```
↓

**8. Store results in database:**
- Split into manageable chunks
- Generate summary statistics
- Calculate pagination metadata

```python
# Database storage structure
{
    "job_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
    "status": "COMPLETED",
    "results": {
        "summary": {
            "total_records": 25,
            "repos_found": 8,
            "identifiers": ["example.com", "@example.com"],
            "git_depth": 20
        },
        "chunks": [
            {
                "chunk_id": 0,
                "data": [/* 25 processed results */]
            }
        ]
    }
}
```
↓

**9. Return response to client:**
- If task pending: return job_id + status
- If completed: return paginated results + summary
- Support cache_hit for repeated requests

```json
{
    "job_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
    "status": "COMPLETED",
    "feature": "git_search",
    "start_time": "2024-01-20T12:00:00Z",
    "end_time": "2024-01-20T12:05:30Z",
    "execution_time": 330,
    "results": {
        "summary": {
            "total_records": 25,
            "total_chunks": 1,
            "repos_found": 8,
            "identifiers": ["example.com", "@example.com"],
            "git_depth": 20,
            "rule_types": ["aws", "openai", "generic"]
        },
        "pagination": {
            "current_page": 1,
            "total_pages": 1,
            "records_per_page": 100,
            "total_records": 25
        },
        "data": [
            {
                "attributes": ["aws-access-key", "AWS Access Key"],
                "context": "AWS_ACCESS_KEY_ID = \"AKIAIOSFODNN7EXAMPLE\"",
                "match": "AKIAIOSFODNN7EXAMPLE",
                "repo": "https://github.com/user/project",
                "url": "https://github.com/user/project/blob/main/config.py",
                "file_last_author": "developer",
                "file_last_updated": "2024-01-15T10:30:00Z",
                "processed_at": "2024-01-20T12:00:00Z",
                "rule_id": "aws-access-key",
                "rule_description": "AWS Access Key",
                "repo_owner": "user",
                "repo_name": "project"
            }
        ]
    }
}
```

### Key Components Interaction

**API Layer** (`app/routers/cti/git_search.py`)
- Handles HTTP requests
- Validates parameters
- Manages task lifecycle
- Returns formatted responses

**Task Layer** (`app/tasks/cti/git_search.py`) 
- Executes git-hound commands asynchronously
- Processes raw output
- Stores structured results
- Updates task status

**Detection Engine** (`app/data/cti/git_search/githound-rules/`)
- YAML rule definitions by service type
- Regular expression patterns
- Rule metadata (name, id, examples)

**Data Storage**
- MongoDB for result persistence
- Chunked storage for large result sets
- Metadata for pagination and caching



# NOTES

## TODO
- enable more dig deep flags of git-hound `--dig-commits` `--dig-files` `--many-results` 

## Current Issues
using dig deep flags sometimes causes infinite running 


# Git Search Feature - Technical Documentation (AI GENERATED)

GitHub repository scanning using modified `git-hound` tool for secret detection.(https://github.com/CurlSek/git-hound)

## Architecture

### Components

1. **FastAPI Router** (`app/routers/cti/git_search.py`)
   - Unified REST API endpoint
   - Handles search requests and pagination
   - Manages task status and caching

2. **Celery Task** (`app/tasks/cti/git_search.py`)
   - Asynchronous background processing
   - Executes git-hound commands
   - Processes and stores results

3. **Detection Rules** (`app/data/cti/git_search/githound-rules/`)
   - YAML-based rule definitions
   - Service-specific and generic patterns
   - Regular expressions for pattern matching

## API Endpoints

### Search for GitHub Leaks

**Endpoint:** `GET /git_search`

**Description:** Search for GitHub leaks containing the specified identifiers using git-hound tool.

#### Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `identifiers` | string | Yes | - | JSON array of identifiers to search for (e.g., domains, emails) |
| `git_depth` | integer | No | 10 | Number of git-hound pages to search (1-50) |
| `job_id` | string | No | - | Task ID from a previous request |
| `ignore_cache` | boolean | No | false | Force a new scan, bypassing stored results |
| `page` | integer | No | 1 | Page number for detailed results |
| `records_per_page` | integer | No | 100 | Number of records per page (10-500) |

#### Example Requests

**Start a new search:**
```bash
curl -X GET "/git_search?identifiers=[\"example.com\",\"@example.com\"]&git_depth=20"
```

**Check status of existing search:**
```bash
curl -X GET "/git_search?job_id=12345678-1234-1234-1234-123456789012"
```

**Get paginated results:**
```bash
curl -X GET "/git_search?job_id=12345678-1234-1234-1234-123456789012&page=2&records_per_page=50"
```

#### Response Format

**Pending/Running Task:**
```json
{
  "job_id": "12345678-1234-1234-1234-123456789012",
  "status": "PENDING",
  "feature": "git_search",
  "cache_hit": false
}
```

**Completed Task with Results:**
```json
{
  "job_id": "12345678-1234-1234-1234-123456789012",
  "status": "COMPLETED",
  "feature": "git_search",
  "start_time": "2024-01-01T12:00:00Z",
  "end_time": "2024-01-01T12:05:00Z",
  "execution_time": 300,
  "results": {
    "summary": {
      "total_records": 25,
      "total_chunks": 1,
      "chunk_size": 100,
      "repos_found": 5,
      "identifiers": ["example.com", "@example.com"],
      "git_depth": 20,
      "date_range": {
        "min": "2023-01-01T00:00:00Z",
        "max": "2024-01-01T00:00:00Z"
      },
      "rule_types": ["aws", "generic", "google"]
    },
    "pagination": {
      "current_page": 1,
      "total_pages": 1,
      "records_per_page": 100,
      "total_records": 25
    },
    "data": [
      {
        "attributes": ["aws-access-key", "AWS Access Key"],
        "context": "AWS access key found in configuration",
        "match": "AKIAIOSFODNN7EXAMPLE",
        "repo": "https://github.com/user/repo",
        "url": "https://github.com/user/repo/blob/main/config.py",
        "file_last_author": "user123",
        "file_last_updated": "2024-01-01T10:00:00Z",
        "processed_at": "2024-01-01T12:00:00Z",
        "rule_id": "aws-access-key",
        "rule_description": "AWS Access Key",
        "repo_owner": "user",
        "repo_name": "repo"
      }
    ]
  }
}
```

## Detection Rules

The Git Search feature uses a comprehensive set of YAML-based rules organized by service/platform:

### Rule Categories

#### Cloud Providers
- **AWS** (`aws.yml`) - Access keys, secret keys, session tokens
- **Azure** (`azure.yml`) - Service principal credentials, storage keys
- **Google Cloud** (`google.yml`) - Service account keys, API keys

#### Code Platforms  
- **GitHub** (`github.yml`) - Personal access tokens, SSH keys
- **GitLab** (`gitlab.yml`) - Access tokens, deploy keys

#### Development Tools
- **Docker** (`dockerconfig.yml`, `dockerhub.yml`) - Registry credentials
- **NPM** (`npm.yml`) - Authentication tokens
- **PyPI** (`pypi.yml`) - API tokens

#### Communication & Collaboration
- **Slack** (`slack.yml`) - Bot tokens, webhooks
- **Microsoft Teams** (`microsoft_teams.yml`) - Webhooks, connectors

#### Databases
- **ODBC** (`odbc.yml`) - Connection strings
- **Generic Database** - Connection parameters

#### Generic Patterns
- **Generic** (`generic.yml`) - API keys, passwords, secrets
- **JWT** (`jwt.yml`) - JSON Web Tokens
- **Hashes** (`hashes.yml`) - Password hashes

### Rule Structure

Each rule file follows this YAML structure:

```yaml
rules:
- name: Rule Name
  id: unique.rule.id
  pattern: (?i:regex_pattern_here)
  examples:
  - 'positive example that should match'
  negative_examples:
  - 'negative example that should not match'
```

### Example Rule

```yaml
- name: AWS Access Key
  id: aws.access-key
  pattern: (?i:aws_access_key_id|access_key_id|aws_access_key).{0,5}['\"]?([A-Z0-9]{20})['\"]?
  examples:
  - 'AWS_ACCESS_KEY_ID = "AKIAIOSFODNN7EXAMPLE"'
  - 'access_key_id: AKIAIOSFODNN7EXAMPLE'
  negative_examples:
  - 'AWS_ACCESS_KEY_ID = "YOUR_ACCESS_KEY_HERE"'
```

## Configuration

### Environment Variables

```bash
# Required: GitHub tokens for API access (comma-separated)
GIT_TOKENS=ghp_token1,ghp_token2,ghp_token3

# Optional: Database connection string
DATABASE_URL=mongodb://localhost:27017/threatmesh
```

### Git-Hound Parameters

- **Threads:** 100 (default)
- **Timeout:** 3600 seconds (1 hour)
- **Output Format:** JSON Lines (JSONL)
- **Rules Directory:** `app/data/cti/git_search/githound-rules/`

## Usage Examples

### Basic Domain Search

Search for leaks containing a specific domain:

```python
import requests

response = requests.get(
    "/git_search",
    params={
        "identifiers": '["example.com"]',
        "git_depth": 10
    }
)

job_id = response.json()["job_id"]
```

### Multi-Identifier Search

Search for multiple related identifiers:

```python
identifiers = [
    "example.com",
    "@example.com", 
    "com.example.app",
    "example-api-key"
]

response = requests.get(
    "/git_search",
    params={
        "identifiers": json.dumps(identifiers),
        "git_depth": 25
    }
)
```

### Polling for Results

```python
import time

# Start search
response = requests.get("/git_search", params={"identifiers": '["example.com"]'})
job_id = response.json()["job_id"]

# Poll for completion
while True:
    status_response = requests.get("/git_search", params={"job_id": job_id})
    status = status_response.json()["status"]
    
    if status == "COMPLETED":
        results = status_response.json()["results"]
        break
    elif status == "FAILED":
        print("Search failed")
        break
        
    time.sleep(30)  # Wait 30 seconds before next check
```

### Paginated Results

```python
# Get first page of results
response = requests.get("/git_search", params={
    "job_id": job_id,
    "page": 1,
    "records_per_page": 50
})

results = response.json()["results"]
total_pages = results["pagination"]["total_pages"]

# Get remaining pages
for page in range(2, total_pages + 1):
    page_response = requests.get("/git_search", params={
        "job_id": job_id,
        "page": page,
        "records_per_page": 50
    })
    # Process page results...
```

## Data Processing

### Result Processing Pipeline

1. **Raw Output Parsing**
   - Parse JSONL output from git-hound
   - Filter valid JSON objects
   - Extract structured data

2. **Data Enrichment**
   - Add processing timestamps
   - Extract repository metadata
   - Normalize field names

3. **Chunked Storage**
   - Split results into manageable chunks
   - Store in database with pagination support
   - Generate summary statistics

### Result Fields

Each result contains:

| Field | Description |
|-------|-------------|
| `attributes` | Rule attributes [rule_id, description] |
| `context` | Surrounding code context |
| `match` | The actual matched secret/pattern |
| `repo` | Repository URL |
| `url` | Direct file URL |
| `file_last_author` | Last commit author |
| `file_last_updated` | Last modification date |
| `processed_at` | Processing timestamp |
| `rule_id` | Matched rule identifier |
| `rule_description` | Human-readable rule description |
| `repo_owner` | Repository owner |
| `repo_name` | Repository name |

## Logging

Log levels used:
- **INFO:** Normal operation, search progress
- **WARNING:** Parsing errors, invalid data  
- **ERROR:** Command failures, system errors
- **DEBUG:** Detailed execution traces 