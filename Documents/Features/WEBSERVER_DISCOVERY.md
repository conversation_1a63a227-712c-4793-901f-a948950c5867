# Webserver Discovery API

## Overview
The Webserver Discovery API identifies active web servers from a list of subdomains using the httpx tool. This API provides two methods:

1. **GET** - Use stored subdomain results from previous subdomain enumeration
2. **POST** - Provide a direct list of subdomains to check

For technology detection on discovered webservers, use the separate [Technology Detection API](TECH_DETECTION.md).

## Data Flow Explanation

### Complete Request Flow - GET Method (Using Stored Subdomains)

**1. Receive API request with domain + parameters**
```http
GET /asm/webservers?domain=example.com&ignore_cache=false
```
↓

**2. Validate request parameters (domain, job_id, ignore_cache)**
```python
# Validated parameters
{
    "domain": "example.com",
    "job_id": None,
    "ignore_cache": False
}
```
↓

**3. Check for existing job_id or create new task**
```python
# If job_id exists, return cached result
# If new request, generate new job_id
job_id = "web-abc123-def456-ghi789"
```
↓

**4. Retrieve subdomain enumeration results for the domain**
```python
# Query database for most recent subdomain results
subdomain_results = get_subdomain_results(domain="example.com")
subdomains_to_check = [
    "api.example.com",
    "blog.example.com", 
    "admin.example.com",
    "mail.example.com",
    "cdn.example.com"
]
```
↓

**5. Create Celery background task job**
```python
task = run_webserver_discovery.delay(
    subdomains=subdomains_to_check,
    job_id="web-abc123-def456-ghi789"
)
```
↓

**6. Execute httpx command for web server discovery:**
- Tool: httpx (HTTP toolkit for probing)
- Protocol support: HTTP and HTTPS
- Timeout: Default httpx timeout settings
- Features: Follow redirects, extract metadata

```bash
# Create temporary file with subdomains and run httpx
/opt/httpx -l /tmp/subdomains.txt -json -silent
```
↓

**7. Httpx executes comprehensive web server probing:**
- HTTP/HTTPS protocol detection
- Status code identification
- Content length measurement
- Title extraction from HTML
- Web server header analysis
- Technology fingerprinting
- Response time measurement

```json
# Sample raw JSON output from httpx
{"url":"https://api.example.com","host":"api.example.com","scheme":"https","port":"443","status_code":200,"content_length":1234,"title":"API Server","webserver":"nginx/1.18.0","tech":["nginx","json-api"],"time":"245ms","a":["*************"],"location":"https://api.example.com/v1","content_type":"application/json","cdn":false}
{"url":"http://blog.example.com","host":"blog.example.com","scheme":"http","port":"80","status_code":200,"content_length":5678,"title":"Company Blog","webserver":"Apache/2.4.41","tech":["apache","wordpress","php"],"time":"189ms","a":["*************"],"words":850,"lines":120,"content_type":"text/html"}
{"url":"https://admin.example.com","host":"admin.example.com","scheme":"https","port":"443","status_code":403,"content_length":162,"title":"Forbidden","webserver":"nginx/1.18.0","tech":["nginx"],"time":"156ms","a":["*************"],"cdn":true,"cdn_name":"cloudflare","cdn_type":"cdn"}
```
↓

**8. Parse and process httpx results:**
- Filter valid JSON objects from output
- Extract webserver information and metadata
- Calculate statistics across discovered servers
- Group by protocols, status codes, and technologies

```python
# Processed result structure
{
    "webservers": [
        {
            "url": "https://api.example.com",
            "location": "https://api.example.com/v1",
            "title": "API Server",
            "webserver": "nginx/1.18.0",
            "content_type": "application/json",
            "response_time": "245ms",
            "a_records": ["*************"],
            "aaaa_records": [],
            "tech": ["nginx", "json-api"],
            "words": 0,
            "lines": 0,
            "status_code": 200,
            "content_length": 1234,
            "cdn": false,
            "cdn_name": "",
            "cdn_type": "",
            "knowledgebase": {}
        },
        {
            "url": "http://blog.example.com",
            "location": "",
            "title": "Company Blog",
            "webserver": "Apache/2.4.41",
            "content_type": "text/html",
            "response_time": "189ms",
            "a_records": ["*************"],
            "aaaa_records": [],
            "tech": ["apache", "wordpress", "php"],
            "words": 850,
            "lines": 120,
            "status_code": 200,
            "content_length": 5678,
            "cdn": false,
            "cdn_name": "",
            "cdn_type": "",
            "knowledgebase": {}
        }
    ],
    "statistics": {
        "protocols": {"http": 1, "https": 2},
        "status_codes": {"200": 2, "403": 1},
        "technologies": ["nginx", "json-api", "apache", "wordpress", "php"],
        "total_subdomains_checked": 5,
        "responsive_servers": 3
    }
}
```
↓

**9. Store results in database:**
- Store complete webserver discovery data
- Store server statistics and metadata
- Clear raw httpx output to save space
- Update task status and completion time

```python
# Database storage structure
{
    "job_id": "web-abc123-def456-ghi789",
    "status": "COMPLETED",
    "feature": "webserver_discovery",
    "structured_output": {
        "webservers": [...],
        "count": 3,
        "statistics": {
            "protocols": {"http": 1, "https": 2},
            "status_codes": {"200": 2, "403": 1},
            "technologies": ["nginx", "json-api", "apache", "wordpress", "php"],
            "total_subdomains_checked": 5,
            "responsive_servers": 3
        }
    },
    "raw_output": []  # Cleared to save space
}
```
↓

**10. Return response to client:**
- If task pending: return job_id + status
- If completed: return webserver list + statistics
- Support cache_hit for repeated requests

```json
{
    "job_id": "web-abc123-def456-ghi789",
    "status": "completed",
    "feature": "webserver_discovery",
    "start_time": "2024-01-20T12:00:00Z",
    "end_time": "2024-01-20T12:01:30Z",
    "execution_time": 90,
    "results": {
        "webservers": [
            {
                "url": "https://api.example.com",
                "location": "https://api.example.com/v1",
                "title": "API Server",
                "webserver": "nginx/1.18.0",
                "content_type": "application/json",
                "response_time": "245ms",
                "a_records": ["*************"],
                "aaaa_records": [],
                "tech": ["nginx", "json-api"],
                "words": 0,
                "lines": 0,
                "status_code": 200,
                "content_length": 1234,
                "cdn": false,
                "cdn_name": "",
                "cdn_type": "",
                "knowledgebase": {}
            }
        ],
        "count": 3,
        "statistics": {
            "protocols": {"http": 1, "https": 2},
            "status_codes": {"200": 2, "403": 1},
            "technologies": ["nginx", "json-api", "apache", "wordpress", "php"],
            "total_subdomains_checked": 5,
            "responsive_servers": 3
        }
    },
    "cache_hit": false
}
```

### Complete Request Flow - POST Method (Direct Subdomain List)

**1. Receive API request with subdomain list**
```http
POST /asm/webservers?ignore_cache=false
Content-Type: application/json

{
  "subdomains": [
    "api.example.com",
    "blog.example.com",
    "admin.example.com"
  ]
}
```
↓

**2. Validate request parameters and subdomain list**
```python
# Validated parameters
{
    "subdomains": ["api.example.com", "blog.example.com", "admin.example.com"],
    "job_id": None,
    "ignore_cache": False
}
# Subdomain validation: Valid format, non-empty list
```
↓

**3. Generate job_id and check cache**
```python
# Generate unique job_id for this subdomain set
job_id = "web-xyz789-abc123-def456"

# Check cache based on subdomain list hash
cache_key = hash(sorted(subdomains))
```
↓

**4. Create Celery background task job**
```python
task = run_webserver_discovery.delay(
    subdomains=["api.example.com", "blog.example.com", "admin.example.com"],
    job_id="web-xyz789-abc123-def456"
)
```
↓

**5-10. Same httpx execution and processing steps as GET method flow**

### Key Components Interaction

**API Layer** (`app/routers/asm/webservers.py`)
- Handles HTTP requests for `/asm/webservers`
- Validates domain parameters and subdomain lists
- Manages task lifecycle and caching
- Integrates with subdomain enumeration results
- Returns formatted webserver discovery responses

**Task Layer** (`app/tasks/asm/webserver_discovery.py`) 
- Executes httpx commands asynchronously
- Processes JSON output from httpx
- Handles parallel subdomain probing and error recovery
- Updates task status and stores comprehensive results

**Discovery Tool** (`httpx`)
- Fast HTTP toolkit for web probing
- Supports both HTTP and HTTPS protocols
- Extracts comprehensive server metadata
- Handles redirects and timeouts gracefully

**Data Storage**
- MongoDB for result persistence
- Structured output with webserver details and statistics
- Raw output cleared after processing to save space
- Cache expiration: 12 hours (configurable)

### Httpx Detection Capabilities

**Protocol Support**
- HTTP (port 80) and HTTPS (port 443)
- Automatic protocol detection
- Custom port discovery
- Redirect following

**Metadata Extraction**
- HTTP status codes (200, 404, 403, 500, etc.)
- Content-Length headers
- HTML title tags
- Server headers (nginx, Apache, IIS)
- Response time measurement
- Content-Type headers
- Location headers (redirects)
- IPv4 addresses (A records)
- IPv6 addresses (AAAA records)
- Word and line counts
- CDN detection and identification
- Knowledge base information

**Technology Detection**
- Basic technology fingerprinting
- Server software identification
- Framework detection (limited)
- CDN identification

**Performance Features**
- Concurrent request processing
- Configurable timeouts
- Rate limiting support
- Error handling and retry logic

### Cache Management

**Cache Duration: 12 hours**
```python
# Cache configuration
TASK_EXPIRATION = {
    "webserver_discovery": timedelta(hours=12)
}

# Cache behavior
- Running tasks: Always return existing task
- Completed tasks: Return cached results within 12 hours
- Expired cache: Start new webserver discovery
- ignore_cache=true: Force new scan regardless of cache
```

**Cache Benefits:**
- Reduces redundant HTTP probing
- Faster response times for recent webserver scans
- Preserves network resources and rate limits
- Maintains consistency in server availability data

## Endpoints

### GET `/asm/webservers` - Use Stored Subdomain Results
Use previously discovered subdomains from the subdomain enumeration API or check task status.

**Query Parameters:**
- `domain` (string, optional): Target domain to use stored subdomain results (e.g., `example.com`)
- `job_id` (string, optional): Task ID from a previous request to check status
- `ignore_cache` (boolean, optional): Force a new scan, bypassing stored results (default: `false`)

Either `domain` or `job_id` must be provided.

### POST `/asm/webservers` - Provide Direct Subdomain List
Run webserver discovery on a specific list of subdomains.

**Query Parameters:**
- `job_id` (string, optional): Task ID from a previous request to check status
- `ignore_cache` (boolean, optional): Force a new scan, bypassing stored results (default: `false`)

**Request Body:**
```json
{
  "subdomains": ["api.example.com", "blog.example.com", "admin.example.com"]
}
```

## Usage Examples

### 1. GET - Use stored subdomain results for a domain
```bash
curl -X GET "http://localhost:8000/asm/webservers?domain=example.com"
```

### 2. GET - Check task status
```bash
curl -X GET "http://localhost:8000/asm/webservers?job_id=550e8400-e29b-41d4-a716-446655440000"
```

### 3. GET - Force new scan with stored subdomains (ignore cache)
```bash
curl -X GET "http://localhost:8000/asm/webservers?domain=example.com&ignore_cache=true"
```

### 4. POST - Use direct subdomain list
```bash
curl -X POST "http://localhost:8000/asm/webservers" \
  -H "Content-Type: application/json" \
  -d '{
    "subdomains": [
      "api.example.com",
      "blog.example.com", 
      "admin.example.com"
    ]
  }'
```

### 5. POST - Check task status
```bash
curl -X POST "http://localhost:8000/asm/webservers?job_id=550e8400-e29b-41d4-a716-446655440000" \
  -H "Content-Type: application/json" \
  -d '{
    "subdomains": []
  }'
```

### 6. POST - Use direct subdomains with ignore cache
```bash
curl -X POST "http://localhost:8000/asm/webservers?ignore_cache=true" \
  -H "Content-Type: application/json" \
  -d '{
    "subdomains": ["api.example.com", "blog.example.com"]
  }'
```

## Webserver Object Fields

Each webserver object in the response contains the following fields:

- **`url`** (string): Full URL of the discovered webserver
- **`location`** (string): Redirect location if any (from Location header)
- **`title`** (string): HTML title extracted from the page
- **`webserver`** (string): Web server software identified from headers (e.g., "nginx/1.18.0")
- **`content_type`** (string): Content-Type header value
- **`response_time`** (string): Response time for the request
- **`a_records`** (array): IPv4 addresses resolved for the domain
- **`aaaa_records`** (array): IPv6 addresses resolved for the domain
- **`tech`** (array): Technologies detected on the webserver
- **`words`** (integer): Number of words in the response body
- **`lines`** (integer): Number of lines in the response body
- **`status_code`** (integer): HTTP status code returned
- **`content_length`** (integer): Content-Length header value
- **`cdn`** (boolean): Whether CDN was detected
- **`cdn_name`** (string): Name of the CDN provider if detected
- **`cdn_type`** (string): Type of CDN service
- **`knowledgebase`** (object): Additional knowledge base information

## Response Format

### Pending/Running Task Response
```json
{
  "job_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "pending",
  "feature": "webserver_discovery",
  "message": "Task is pending",
  "cache_hit": false
}
```

### Completed Task Response
```json
{
  "job_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "completed",
  "feature": "webserver_discovery",
  "results": {
    "webservers": [
      {
        "url": "https://api.example.com",
        "location": "https://api.example.com/v1",
        "title": "API Server",
        "webserver": "nginx/1.18.0",
        "content_type": "application/json",
        "response_time": "245ms",
        "a_records": ["*************"],
        "aaaa_records": [],
        "tech": ["nginx", "json-api"],
        "words": 0,
        "lines": 0,
        "status_code": 200,
        "content_length": 1234,
        "cdn": false,
        "cdn_name": "",
        "cdn_type": "",
        "knowledgebase": {}
      },
      {
        "url": "http://blog.example.com",
        "location": "",
        "title": "Company Blog",
        "webserver": "Apache/2.4.41",
        "content_type": "text/html",
        "response_time": "189ms",
        "a_records": ["*************"],
        "aaaa_records": [],
        "tech": ["apache", "wordpress", "php"],
        "words": 850,
        "lines": 120,
        "status_code": 200,
        "content_length": 5678,
        "cdn": false,
        "cdn_name": "",
        "cdn_type": "",
        "knowledgebase": {}
      }
    ],
    "count": 2,
    "statistics": {
      "protocols": {
        "http": 1,
        "https": 1
      },
      "status_codes": {
        "200": 2
      },
      "technologies": ["nginx", "json-api", "apache", "wordpress", "php"],
      "total_subdomains_checked": 10,
      "responsive_servers": 2
    }
  },
  "start_time": "2024-01-15T10:30:00Z",
  "end_time": "2024-01-15T10:32:15Z",
  "cache_hit": false
}
```

## Workflow

### GET Request Workflow
1. **Query Parameter Validation**: Validates `domain` format and ensures either `domain` or `job_id` is provided
2. **Task Status Check**: If `job_id` provided, returns existing task status/results
3. **Subdomain Retrieval**: Gets subdomains from most recent successful subdomain enumeration for the domain
4. **Cache Check**: Unless `ignore_cache=true`, checks for recent results
5. **Web Server Discovery**: Starts httpx scan if no recent results found
6. **Result Processing**: Parses httpx JSON output and extracts information

### POST Request Workflow  
1. **Request Validation**: Validates subdomain list in request body
2. **Query Parameter Validation**: Validates query parameters
3. **Task Status Check**: If `job_id` provided, returns existing task status/results
4. **Cache Check**: Unless `ignore_cache=true`, checks for recent results with same subdomain list
5. **Web Server Discovery**: Starts httpx scan on provided subdomains
6. **Result Processing**: Parses httpx JSON output and extracts information

## Error Handling

### GET - Missing Required Parameters
```json
{
  "detail": "Either 'domain' or 'job_id' parameter must be provided for GET request"
}
```

### GET - No Subdomain Results Available
```json
{
  "job_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "failed",
  "feature": "webserver_discovery",
  "error": "No subdomain enumeration results found for domain example.com. Please run subdomain enumeration first.",
  "start_time": "2024-01-15T10:30:00Z",
  "end_time": "2024-01-15T10:30:05Z"
}
```

### POST - Invalid Subdomain in Body
```json
{
  "detail": [
    {
      "type": "value_error",
      "loc": ["body", "subdomains", 0],
      "msg": "Invalid subdomain format: invalid-domain",
      "input": "invalid-domain"
    }
  ]
}
```

### POST - Empty Subdomains List
```json
{
  "detail": [
    {
      "type": "value_error",
      "loc": ["body", "subdomains"],
      "msg": "Subdomains list cannot be empty",
      "input": []
    }
  ]
}
```

### Invalid Domain Format
```json
{
  "detail": "Invalid domain format. Example of valid domain: example.com"
}
```

### Task Not Found
```json
{
  "detail": "Task 550e8400-e29b-41d4-a716-446655440000 not found"
}
```

## Use Cases

### Scenario 1: Systematic Domain Assessment
1. Use `GET /asm/subdomains?domain=example.com` to discover subdomains
2. Use `GET /asm/webservers?domain=example.com` to identify web servers
3. Use `GET /asm/tech-detection?domain=example.com` to detect technologies
4. Use `GET /asm/public-ips?domain=example.com` to get IP addresses

### Scenario 2: Targeted Subdomain Analysis
1. Use `POST /asm/webservers` with specific subdomain list for focused analysis
2. Useful when you have a curated list of high-value targets

### Scenario 3: Continuous Monitoring
1. Use `ignore_cache=true` to force fresh scans
2. Check `job_id` periodically for long-running tasks

## Dependencies
- **httpx**: Used for HTTP probing and web server identification
- **MongoDB**: For storing task results and retrieving subdomain data
- **Celery**: For asynchronous task processing
- **Pydantic**: For request validation and serialization

## Performance Considerations
- **Timeout**: Each subdomain is probed with default httpx timeout settings
- **Retries**: httpx handles retries automatically
- **Caching**: Results are cached for 12 hours by default
- **Parallel Processing**: httpx handles concurrent requests efficiently

## Related APIs
- **Subdomain Enumeration** (`GET /asm/subdomains`): Use this first to discover subdomains for a domain
- **Technology Detection** (`GET /asm/tech-detection`): Use this after webserver discovery to identify technology stacks
- **Public IPs** (`GET /asm/public-ips`): Can be used after webserver discovery to identify IP addresses 