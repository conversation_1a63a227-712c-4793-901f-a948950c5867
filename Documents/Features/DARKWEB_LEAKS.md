# Working

## Data Flow Explanation

### Complete Request Flow

**1. Receive API request with domain + parameters**
```http
GET /darkweb_leaks?domain=example.com&ignore_cache=false&page=1&records_per_page=100
```
↓

**2. Validate request parameters (domain, pagination)**
```python
# Validated parameters
{
    "domain": "example.com",
    "ignore_cache": False,
    "page": 1,
    "records_per_page": 100  # 10-500 range
}
```
↓

**3. Check for existing job_id or create new task**
```python
# If job_id exists, return cached result
# If new request, generate new job_id
job_id = "c3d4e5f6-g7h8-9012-cdef-345678901234"
```
↓

**4. Create Celery background task job**
```python
task = run_darkweb_search_by_domain.delay(
    domain="example.com",
    chunk_size=100
)
```
↓

**5. Execute single API call to internal data source:**
- API returns maximum 10,000 results total (no pagination needed) - DEHASHED LIMIT
- Single request gets all available leaked data for domain
- Rate limiting applied between requests (1-3 second delays)
- De-duplication enabled to remove duplicate entries

```python
# API call with single request
api_payload = {
    "query": "domain:example.com",
    "page": 1,
    "size": 10000,  # Maximum results per request
    "de_dupe": True  # Remove duplicate entries
}

# Headers for authentication
headers = {
    "Content-Type": "application/json",
    "DeHashed-Api-Key": DEHASHED_API_KEY
}
```
↓

**6. Internal API executes search across darkweb leak databases:**
- Searches across multiple breach databases for domain matches
- Returns structured data with credentials, emails, and metadata
- Maximum 10,000 results enforced by API limitations
- Results include various data types: emails, passwords, hashes, IPs

```json
# Sample API response structure
{
    "balance": 100,
    "entries": [
        {
            "id": "5603802198",
            "email": ["<EMAIL>"],
            "ip_address": ["*************"],
            "username": ["<EMAIL>"],
            "password": ["password123"],
            "hashed_password": ["5e884898da28047151d0e56f8dc6292773603d0d6aabbdd62a11ef721d1542d8"],
            "name": ["John Admin"],
            "database_name": "CompanyBreach2023",
            "raw_record": {
                "le_only": true,
                "unstructured": false
            }
        },
        {
            "id": "5603802199", 
            "email": ["<EMAIL>"],
            "username": ["user123"],
            "password": ["mypassword"],
            "database_name": "SocialMediaLeak2024"
        }
    ],
    "took": "245µs",
    "total": 1250
}
```
↓

**7. Parse and process raw results:**
- Extract all entries from API response as-is (no filtering)
- Count unique emails, password types, and databases
- Generate metadata statistics for summary
- Store raw entries in chunks for efficient retrieval

```python
# Processing extracts metadata without modifying entries
processed_metadata = {
    "total_records": 1250,
    "unique_emails": {"<EMAIL>", "<EMAIL>", "..."},
    "databases": {"CompanyBreach2023", "SocialMediaLeak2024", "..."},
    "plain_text_passwords": 845,  # Count of entries with "password" field
    "hashed_passwords": 405,      # Count of entries with "hashed_password" field
}

# Raw entries stored exactly as received from API
raw_entries = [
    {
        "id": "5603802198",
        "email": ["<EMAIL>"],
        "ip_address": ["*************"],
        "username": ["<EMAIL>"],
        "password": ["password123"],
        "hashed_password": ["5e884898da28047151d0e56f8dc6292773603d0d6aabbdd62a11ef721d1542d8"],
        "name": ["John Admin"],
        "database_name": "CompanyBreach2023",
        "raw_record": {"le_only": true, "unstructured": false}
    }
    # ... more entries exactly as received
]
```
↓

**8. Store results in database chunks:**
- Split raw entries into manageable chunks (100 records each)
- Generate summary statistics for the operation
- Store metadata for pagination and filtering
- No data transformation - preserve original API structure

```python
# Database chunk storage structure
{
    "operation_id": "c3d4e5f6-g7h8-9012-cdef-345678901234",
    "domain": "example.com",
    "chunk_number": 0,
    "created_at": "2024-01-20T12:00:00Z",
    "count": 100,
    "data": [/* 100 raw API entries */]
}

# Summary stored in operations collection
{
    "job_id": "c3d4e5f6-g7h8-9012-cdef-345678901234",
    "status": "COMPLETED",
    "structured_output": {
        "search_parameters": {
            "domain": "example.com"
        },
        "summary": {
            "total_records": 1250,
            "total_chunks": 13,
            "chunk_size": 100,
            "unique_emails_count": 456,
            "plain_text_passwords_count": 845,
            "hashed_passwords_count": 405,
            "databases_count": 12
        }
    }
}
```
↓

**9. Return response to client:**
- If task pending: return job_id + status
- If completed: return paginated results + summary
- Support cache_hit for repeated requests (7-day cache)
- Raw API entries returned in "records" field

```json
{
    "job_id": "c3d4e5f6-g7h8-9012-cdef-345678901234",
    "status": "COMPLETED",
    "feature": "darkweb_leaks",
    "start_time": "2024-01-20T12:00:00Z",
    "end_time": "2024-01-20T12:02:15Z",
    "execution_time": 135,
    "results": {
        "search_parameters": {
            "domain": "example.com"
        },
        "summary": {
            "total_records": 1250,
            "total_chunks": 13,
            "chunk_size": 100,
            "unique_emails_count": 456,
            "plain_text_passwords_count": 845,
            "hashed_passwords_count": 405,
            "databases_count": 12
        },
        "records": [
            {
                "id": "5603802198",
                "email": ["<EMAIL>"],
                "ip_address": ["*************"],
                "username": ["<EMAIL>"],
                "password": ["password123"],
                "hashed_password": ["5e884898da28047151d0e56f8dc6292773603d0d6aabbdd62a11ef721d1542d8"],
                "name": ["John Admin"],
                "database_name": "CompanyBreach2023",
                "raw_record": {"le_only": true, "unstructured": false}
            }
        ],
        "pagination": {
            "total_records": 1250,
            "page": 1,
            "total_pages": 13,
            "records_per_page": 100
        }
    }
}
```

### Key Components Interaction

**API Layer** (`app/routers/cti/darkweb_leaks.py`)
- Handles HTTP requests for domain-based searches
- Validates domain parameter and pagination options
- Manages task lifecycle and status checking
- Returns paginated results with raw leak data
- Supports unified endpoint for all operations

**Task Layer** (`app/tasks/cti/darkweb_leaks.py`)
- Executes single API call asynchronously
- Processes raw API responses without modification
- Stores results in chunks for efficient retrieval
- Updates progress through distinct phases
- Generates summary statistics

**Data Storage** (`app/db/operations.py`)
- MongoDB collections for result persistence
- Chunked storage for large result sets (max 10,000)
- Metadata indexing for efficient queries
- Operation tracking for cache management

**Configuration**
- Environment variables for API credentials
- Rate limiting configuration (1-3 second delays)
- Cache expiration settings (7-day default)
- Chunk size configuration (100 records default)

---

## API Reference

### Endpoint
```
GET /darkweb_leaks
```

### Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `domain` | string | Yes | - | Domain to search for in darkweb leaks |
| `job_id` | string | No | null | Task ID from previous request to check status |
| `ignore_cache` | boolean | No | false | Force new scan, bypassing stored results |
| `page` | integer | No | 1 | Page number for detailed results (≥1) |
| `records_per_page` | integer | No | 100 | Records per page (10-500) |

### Response Structure

**Pending Task Response:**
```json
{
    "job_id": "uuid",
    "status": "PENDING",
    "feature": "darkweb_leaks"
}
```

**Running Task Response:**
```json
{
    "job_id": "uuid",
    "status": "RUNNING",
    "feature": "darkweb_leaks",
    "progress_perc": 60,
    "message": "Processing 1250 records"
}
```

**Completed Task Response:**
```json
{
    "job_id": "uuid",
    "status": "COMPLETED",
    "feature": "darkweb_leaks",
    "start_time": "2024-01-20T12:00:00Z",
    "end_time": "2024-01-20T12:02:15Z",
    "execution_time": 135,
    "cache_hit": false,
    "results": {
        "search_parameters": {
            "domain": "example.com"
        },
        "summary": {
            "total_records": 1250,
            "total_chunks": 13,
            "chunk_size": 100,
            "unique_emails_count": 456,
            "plain_text_passwords_count": 845,
            "hashed_passwords_count": 405,
            "databases_count": 12
        },
        "records": [
            {
                "id": "5603802198",
                "email": ["<EMAIL>"],
                "ip_address": ["*************"],
                "username": ["<EMAIL>"],
                "password": ["password123"],
                "hashed_password": ["hash_value"],
                "name": ["John Admin"],
                "database_name": "CompanyBreach2023"
            }
        ],
        "pagination": {
            "total_records": 1250,
            "page": 1,
            "total_pages": 13,
            "records_per_page": 100
        }
    }
}
```

---

## Technical Implementation

### Environment Variables
```bash
# API Configuration
DEHASHED_API_URL=https://api.dehashed.com/v2
DEHASHED_API_KEY=your_api_key_here

# Rate Limiting
DEHASHED_MIN_DELAY=1
DEHASHED_MAX_DELAY=3

# Database
MONGODB_URI=mongodb://localhost:27017
MONGODB_DB=threatmesh
```

### Database Schema

**Operations Collection:**
```javascript
{
    "_id": "c3d4e5f6-g7h8-9012-cdef-345678901234",
    "feature": "darkweb_leaks",
    "parameters": {
        "domain": "example.com"
    },
    "status": "COMPLETED",
    "created_at": ISODate("2024-01-20T12:00:00Z"),
    "start_time": ISODate("2024-01-20T12:00:00Z"),
    "end_time": ISODate("2024-01-20T12:02:15Z"),
    "task_id": "celery_task_id",
    "structured_output": {
        "search_parameters": {
            "domain": "example.com"
        },
        "summary": {
            "total_records": 1250,
            "total_chunks": 13,
            "chunk_size": 100,
            "unique_emails_count": 456,
            "plain_text_passwords_count": 845,
            "hashed_passwords_count": 405,
            "databases_count": 12
        }
    }
}
```

**DarkWeb Leaks Collection:**
```javascript
{
    "operation_id": "c3d4e5f6-g7h8-9012-cdef-345678901234",
    "domain": "example.com",
    "chunk_number": 0,
    "created_at": ISODate("2024-01-20T12:00:00Z"),
    "count": 100,
    "data": [
        {
            "id": "5603802198",
            "email": ["<EMAIL>"],
            "ip_address": ["*************"],
            "username": ["<EMAIL>"],
            "password": ["password123"],
            "hashed_password": ["hash_value"],
            "name": ["John Admin"],
            "database_name": "CompanyBreach2023",
            "raw_record": {
                "le_only": true,
                "unstructured": false
            }
        }
    ]
}
```

### Indexes
```javascript
// Operations collection
db.operations.createIndex({ "feature": 1 })
db.operations.createIndex({ "status": 1 })
db.operations.createIndex({ "parameters.domain": 1 })

// DarkWeb leaks collection
db.darkweb_leaks.createIndex({ "operation_id": 1 })
db.darkweb_leaks.createIndex({ "chunk_number": 1 })
db.darkweb_leaks.createIndex({ "domain": 1 })
db.darkweb_leaks.createIndex({ "database_name": 1 })
```

---

## Performance & Limitations

### API Limitations
- **Maximum Results:** 10,000 records per domain search
- **Rate Limiting:** 1-3 second delays between requests
- **Single Request:** No pagination needed due to result limit
- **Authentication:** API key required for access

### Performance Characteristics
- **Typical Response Time:** 30-180 seconds
- **Memory Usage:** Moderate (raw data storage)
- **Database Impact:** Chunked storage minimizes load
- **Cache Duration:** 7 days for completed results

### Scaling Considerations
- **Concurrent Requests:** Limited by API rate limits
- **Storage Growth:** ~1MB per 1000 records average
- **Index Performance:** Optimized for domain and operation queries
- **Memory Management:** Chunked processing prevents memory issues

---

## Security & Privacy

### Data Handling
- **Raw Data Storage:** Leak data stored as-is from API
- **No Processing:** Passwords and hashes preserved exactly
- **Access Control:** API-level authentication required
- **Audit Trail:** All operations logged with timestamps

### Privacy Considerations
- **Sensitive Data:** Contains real breach credentials
- **Access Logging:** All queries tracked by operation ID
- **Data Retention:** Configurable cache expiration
- **API Anonymization:** No external API references in responses

### Security Measures
- **Environment Variables:** Sensitive config externalized
- **Input Validation:** Domain parameter sanitized
- **Error Handling:** Generic error messages to users
- **Rate Limiting:** Prevents API abuse

---

## Usage Examples

### Basic Domain Search
```bash
curl "http://localhost:8000/darkweb_leaks?domain=example.com"
```

### Check Task Status
```bash
curl "http://localhost:8000/darkweb_leaks?job_id=c3d4e5f6-g7h8-9012-cdef-345678901234"
```

### Paginated Results
```bash
curl "http://localhost:8000/darkweb_leaks?job_id=c3d4e5f6-g7h8-9012-cdef-345678901234&page=2&records_per_page=50"
```

### Force New Scan
```bash
curl "http://localhost:8000/darkweb_leaks?domain=example.com&ignore_cache=true"
```

---

## Error Handling

### Common Errors

**Invalid Domain:**
```json
{
    "detail": "Invalid domain format"
}
```

**Task Not Found:**
```json
{
    "detail": "Task c3d4e5f6-g7h8-9012-cdef-345678901234 not found"
}
```

**API Error:**
```json
{
    "job_id": "c3d4e5f6-g7h8-9012-cdef-345678901234",
    "status": "FAILED",
    "error": "Internal API request failed with status code 429"
}
```

**Invalid Pagination:**
```json
{
    "detail": [
        {
            "loc": ["query", "page"],
            "msg": "ensure this value is greater than or equal to 1",
            "type": "value_error.number.not_ge"
        }
    ]
} 