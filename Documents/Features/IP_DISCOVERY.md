# Working

## Data Flow Explanation

### Complete Request Flow

**1. Receive API request with domain + parameters**
```http
GET /asm/public_ips?domain=example.com&detailed=true&ignore_cache=false
```
↓

**2. Validate request parameters (domain, detailed, job_id, ignore_cache)**
```python
# Validated parameters
{
    "domain": "example.com",
    "detailed": True,  # Include InternetDB enrichment
    "job_id": None,
    "ignore_cache": False
}
```
↓

**3. Check for existing job_id or create new task**
```python
# If job_id exists, return cached result
# If new request, generate new job_id
job_id = "d4e5f6g7-h8i9-0123-defg-h45678901234"
```
↓

**4. Create Celery background task job**
```python
task = run_public_ip_search.delay(
    domain="example.com",
    detailed=True
)
```
↓

**5. Execute Shodan web scraping queries:**
- Data source: Shodan search (via FireProx proxy)
- Query types: SSL certificate CN and hostname searches
- Rate limiting: Built-in delays between requests
- Cache expiration: 1 hour

```python
# Shodan queries executed
queries = [
    "https://proxy.url/search/facet?query=ssl.cert.subject.cn:example.com&facet=ip",
    "https://proxy.url/search/facet?query=hostname:example.com&facet=ip"
]
```
↓

**6. Shodan web scraping executes searches:**
- Searches SSL certificates for domain in subject CN field
- Searches hostname records for exact domain matches
- Parses HTML response to extract IP addresses
- Uses multiple selectors for IP extraction

```html
# Sample Shodan HTML response
<div class="facet-row">
    <div class="name">
        <a href="/host/************">
            <strong>************</strong>
        </a>
    </div>
    <div class="count">15</div>
</div>
<div class="facet-row">
    <div class="name">
        <a href="/host/************">
            <strong>************</strong>
        </a>
    </div>
    <div class="count">8</div>
</div>
```
↓

**7. Parse HTML and extract IPs + optional enrichment:**
- Extract IPs using CSS selectors (div.facet-row div.name a strong)
- If detailed=true: Query Shodan InternetDB for each IP
- Enrich with ports, services, vulnerabilities, hostnames
- Add random delays between InternetDB queries

```python
# Basic IP extraction
extracted_ips = ["************", "************", "************"]

# If detailed=true, enhanced with InternetDB data
{
    "ip": "************",
    "source": "shodan",
    "ports": [80, 443, 8080],
    "hostnames": ["web1.example.com", "www.example.com"],
    "tags": ["cloud"],
    "vulns": ["CVE-2021-44228"],
    "cpes": ["cpe:2.3:a:apache:http_server:2.4.41"],
    "services": [
        {
            "name": "http_server",
            "vendor": "apache", 
            "version": "2.4.41"
        }
    ]
}
```
↓

**8. Store results in database:**
- Store IP list with source attribution
- Include enrichment data if detailed=true
- Generate summary statistics
- Track unique IPs and sources

```python
# Database storage structure
{
    "job_id": "d4e5f6g7-h8i9-0123-defg-h45678901234",
    "status": "COMPLETED",
    "structured_output": {
        "ips": [
            {
                "ip": "************",
                "source": "shodan",
                "ports": [80, 443, 8080],
                "hostnames": ["web1.example.com", "www.example.com"],
                "tags": ["cloud"],
                "vulns": ["CVE-2021-44228"],
                "services": [{"name": "http_server", "vendor": "apache", "version": "2.4.41"}]
            }
        ],
        "sources": {
            "shodan": 3
        },
        "count": 3,
        "detailed_analysis": True
    }
}
```
↓

**9. Return response to client:**
- If task pending: return job_id + status
- If completed: return IP list with optional enrichment
- Support cache_hit for repeated requests

```json
{
    "job_id": "d4e5f6g7-h8i9-0123-defg-h45678901234",
    "status": "completed",
    "feature": "public_ips",
    "start_time": "2024-01-20T12:00:00Z",
    "end_time": "2024-01-20T12:01:30Z",
    "execution_time": 90,
    "results": {
        "ips": [
            {
                "ip": "************",
                "source": "shodan",
                "ports": [80, 443, 8080],
                "hostnames": ["web1.example.com", "www.example.com"],
                "tags": ["cloud"],
                "vulns": ["CVE-2021-44228"],
                "cpes": ["cpe:2.3:a:apache:http_server:2.4.41"],
                "services": [
                    {
                        "name": "http_server",
                        "vendor": "apache",
                        "version": "2.4.41"
                    }
                ]
            },
            {
                "ip": "************",
                "source": "shodan",
                "ports": [22, 80, 443],
                "hostnames": ["mail.example.com"],
                "tags": [],
                "vulns": [],
                "services": []
            }
        ],
        "sources": {
            "shodan": 3
        },
        "count": 3
    },
    "cache_hit": false
}
```

### Key Components Interaction

**API Layer** (`app/routers/asm/public_ips.py`)
- Handles HTTP requests for `/asm/public_ips`
- Validates domain and detailed parameters
- Manages task lifecycle and caching
- Returns formatted responses

**Task Layer** (`app/tasks/asm/public_ip_search.py`) 
- Executes Shodan web scraping asynchronously
- Parses HTML responses to extract IPs
- Queries InternetDB for enrichment data
- Handles rate limiting and error recovery

**Data Sources**
- **Shodan Search**: Web scraping via FireProx proxy
- **InternetDB**: REST API for IP enrichment data
- **Multiple query types**: SSL certs + hostname searches

**Data Storage**
- MongoDB for result persistence
- IP list with source attribution
- Optional enrichment data (ports, services, vulnerabilities)
- Cache expiration: 1 hour (configurable)

### Shodan Query Types

**SSL Certificate Subject CN Search**
```
query=ssl.cert.subject.cn:example.com&facet=ip
```
- Finds IPs with SSL certificates containing the domain
- Common for web servers and services
- Includes subdomains in certificate subject

**Hostname Search**
```
query=hostname:example.com&facet=ip
```
- Finds IPs with reverse DNS pointing to domain
- Identifies mail servers, DNS servers
- Direct hostname-to-IP mappings

### InternetDB Enrichment (when detailed=true)

**Available Data Fields:**
- **ports**: List of open ports discovered by Shodan
- **hostnames**: Reverse DNS hostnames for the IP
- **tags**: Service/cloud provider tags
- **vulns**: Known CVE vulnerabilities
- **cpes**: Common Platform Enumeration identifiers
- **services**: Extracted service information from CPEs

**Rate Limiting:**
```python
# InternetDB query delays
INTERNETDB_MIN_DELAY = 1  # Minimum 1 second
INTERNETDB_MAX_DELAY = 3  # Maximum 3 seconds
# Random delay between queries to avoid rate limits
```

### Cache Management

**Cache Duration: 1 hour**
```python
# Cache configuration
TASK_EXPIRATION = {
    "public_ips": timedelta(hours=1)
}

# Cache behavior
- Running tasks: Always return existing task
- Completed tasks: Return cached results within 1 hour
- Expired cache: Start new IP discovery
- ignore_cache=true: Force new scan regardless of cache
```

**Short Cache Benefits:**
- IP assignments can change frequently
- SSL certificates may be updated
- New services may be deployed
- Balance between freshness and API limits

### Error Handling

**HTML Parsing Fallbacks:**
1. Primary selector: `div.facet-row div.name a strong`
2. Alternative selector: `div.name a strong`
3. Regex fallback: `<strong>(\d+\.\d+\.\d+\.\d+)</strong>`

**API Resilience:**
- Multiple Shodan query types for redundancy
- InternetDB failures don't block main results
- Detailed error logging for debugging
- Graceful degradation when enrichment fails 