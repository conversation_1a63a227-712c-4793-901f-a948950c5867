# Working

## Data Flow Explanation

### Complete Request Flow

**1. Receive API request with domain + parameters**
```http
GET /asm/subdomains?domain=example.com&ignore_cache=false
```
↓

**2. Validate request parameters (domain, job_id, ignore_cache)**
```python
# Validated parameters
{
    "domain": "example.com",
    "job_id": None,
    "ignore_cache": False,
    "detailed": False
}
```
↓

**3. Check for existing job_id or create new task**
```python
# If job_id exists, return cached result
# If new request, generate new job_id
job_id = "c3d4e5f6-g7h8-9012-cdef-g34567890123"
```
↓

**4. Create Celery background task job**
```python
task = run_subdomain_enumeration.delay(
    domain="example.com"
)
```
↓

**5. Execute subfinder command for subdomain enumeration:**
- Tool: subfinder (open source subdomain discovery tool)
- Output format: JSON Lines (JSONL)
- Sources: Certificate transparency, DNS records, search engines
- Cache expiration: 7 hours

```bash
subfinder -d example.com -silent -json
```
↓

**6. Subfinder executes enumeration across multiple sources:**
- Certificate transparency logs (crt.sh, censys)
- DNS enumeration (hackertarget, dnsdumpster)
- Search engines (google, bing, yahoo)
- API sources (virustotal, urlscan, alienvault)

```json
# Sample raw JSONL output from subfinder
{"host":"www.example.com","source":"crtsh"}
{"host":"api.example.com","source":"alienvault"}
{"host":"mail.example.com","source":"hackertarget"}
{"host":"blog.example.com","source":"crtsh"}
{"host":"cdn.example.com","source":"dnsdumpster"}
```
↓

**7. Parse and process subfinder results:**
- Filter valid JSON objects from JSONL output
- Extract unique subdomains
- Track sources for each subdomain
- Generate source statistics

```python
# Processed result structure
{
    "subdomains": [
        "api.example.com",
        "blog.example.com", 
        "cdn.example.com",
        "mail.example.com",
        "www.example.com"
    ],
    "sources": {
        "crtsh": 3,           # Found 3 subdomains
        "alienvault": 1,      # Found 1 subdomain
        "hackertarget": 2,    # Found 2 subdomains
        "dnsdumpster": 1      # Found 1 subdomain
    },
    "count": 5
}
```
↓

**8. Store results in database:**
- Store complete subdomain list
- Store source attribution data
- Clear raw output to save space
- Generate summary statistics

```python
# Database storage structure
{
    "job_id": "c3d4e5f6-g7h8-9012-cdef-g34567890123",
    "status": "COMPLETED",
    "structured_output": {
        "subdomains": ["api.example.com", "blog.example.com", "cdn.example.com", "mail.example.com", "www.example.com"],
        "sources": {
            "crtsh": 3,
            "alienvault": 1,
            "hackertarget": 2,
            "dnsdumpster": 1
        },
        "count": 5
    },
    "raw_output": []  # Cleared to save space
}
```
↓

**9. Return response to client:**
- If task pending: return job_id + status
- If completed: return subdomain list + source statistics
- Support cache_hit for repeated requests

```json
{
    "job_id": "c3d4e5f6-g7h8-9012-cdef-g34567890123",
    "status": "completed",
    "feature": "subdomain_enumeration",
    "start_time": "2024-01-20T12:00:00Z",
    "end_time": "2024-01-20T12:02:15Z",
    "execution_time": 135,
    "results": {
        "subdomains": [
            "api.example.com",
            "blog.example.com",
            "cdn.example.com", 
            "mail.example.com",
            "www.example.com"
        ],
        "sources": {
            "crtsh": 3,
            "alienvault": 1,
            "hackertarget": 2,
            "dnsdumpster": 1
        },
        "count": 5
    },
    "cache_hit": false
}
```

### Key Components Interaction

**API Layer** (`app/routers/asm/subdomains.py`)
- Handles HTTP requests for `/asm/subdomains`
- Validates domain parameters
- Manages task lifecycle and caching
- Returns formatted responses

**Task Layer** (`app/tasks/asm/subdomain_enumeration.py`) 
- Executes subfinder commands asynchronously
- Processes JSONL output from subfinder
- Handles source attribution and deduplication
- Updates task status and stores results

**Enumeration Tool** (`subfinder`)
- Open source subdomain discovery tool
- Queries multiple passive sources simultaneously
- Outputs structured JSON data
- No API keys required for basic functionality

**Data Storage**
- MongoDB for result persistence
- Structured output with subdomain list and sources
- Raw output cleared after processing to save space
- Cache expiration: 7 hours (configurable)

### Subfinder Data Sources

**Certificate Transparency**
- crt.sh - Certificate transparency logs
- censys - Internet-wide scanning data
- certspotter - Certificate monitoring

**DNS Enumeration**
- hackertarget - DNS enumeration service
- dnsdumpster - DNS recon service
- threatcrowd - Open source threat intelligence

**Search Engines**
- google - Google search results
- bing - Bing search results 
- yahoo - Yahoo search results

**API Sources**
- virustotal - URL/domain scanning service
- urlscan - Website scanner
- alienvault - Threat intelligence platform

### Cache Management

**Cache Duration: 7 hours**
```python
# Cache configuration
TASK_EXPIRATION = {
    "subdomain_enumeration": timedelta(hours=7)
}

# Cache behavior
- Running tasks: Always return existing task
- Completed tasks: Return cached results within 7 hours
- Expired cache: Start new enumeration
- ignore_cache=true: Force new scan regardless of cache
```

**Cache Benefits:**
- Reduces redundant API calls to external sources
- Faster response times for recent queries
- Preserves rate limits on external services
- Maintains consistency in results over short periods 