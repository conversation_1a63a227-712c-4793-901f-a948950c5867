# ThreatMesh Docker Setup

This guide explains how to run the complete ThreatMesh application using Docker Compose.

## Architecture

The application consists of the following services:

- **API Service**: FastAPI application serving the REST API
- **Worker Service**: Celery workers processing background tasks
- **MongoDB**: Database for storing results and application data (MongoDB 7.0)
- **Redis**: Message broker for Celery and caching (Redis 7.2)

## Prerequisites

- Docker Engine 20.10+
- Docker Compose 2.0+
- At least 4GB RAM available for containers

## Quick Start

1. **Clone the repository** (if not already done):
   ```bash
   git clone <repository-url>
   cd threatmesh
   ```

2. **Set up environment variables**:
   ```bash
   cp env.example .env
   ```
   add keys to .env file

3. **Build and start all services**:
   ```bash
   docker-compose up --build
   ```

   Or run in detached mode:
   ```bash
   docker-compose up --build -d
   ```

4. **Verify the setup**:
   - API: http://localhost:8000
   - API Documentation: http://localhost:8000/docs
   - Health Check: http://localhost:8000/health

## Services Overview

### API Service (Port 8000)
- **Container**: `threatmesh-api`
- **Endpoint**: http://localhost:8000
- **Health Check**: http://localhost:8000/health
- **Documentation**: http://localhost:8000/docs
- **Framework**: FastAPI with automatic reload in development

### Worker Service
- **Container**: `threatmesh-worker`
- **Function**: Processes background tasks (subdomain enumeration, vulnerability analysis, etc.)
- **Concurrency**: 4 workers by default
- **Technologies**: Includes Go tools (subfinder, httpx), git-hound, Wappalyzer CLI

### MongoDB (Port 27017)
- **Container**: `threatmesh-mongodb`
- **Version**: MongoDB 7.0
- **Database**: `threatmesh`
- **Persistence**: Data stored in `mongodb_data` volume
- **Health Check**: Uses `mongosh` for connection testing

### Redis (Port 6379)
- **Container**: `threatmesh-redis`
- **Version**: Redis 7.2 Alpine
- **Function**: Message broker and caching
- **Persistence**: Data stored in `redis_data` volume with AOF enabled

## Available Commands

### Start Services
```bash
# Start all services
docker-compose up

# Start in background
docker-compose up -d

# Rebuild and start
docker-compose up --build
```

### Stop Services
```bash
# Stop all services
docker-compose down

# Stop and remove volumes
docker-compose down -v
```

### View Logs
```bash
# All services
docker-compose logs

# Specific service
docker-compose logs api
docker-compose logs worker
docker-compose logs mongodb
docker-compose logs redis

# Follow logs
docker-compose logs -f api
```

### Scale Workers
```bash
# Scale to 3 worker instances
docker-compose up --scale worker=3
```

## Monitoring

### Health Checks
All services include health checks:
```bash
# Check service health
docker-compose ps

# Check API health
curl http://localhost:8000/health

# View health check logs
docker inspect threatmesh-api --format='{{.State.Health.Status}}'
```

### Service Dependencies
- API and Worker services wait for MongoDB and Redis to be healthy before starting
- Automatic restarts are configured for all services

## Data Persistence

Data is persisted in Docker volumes:
- `mongodb_data`: Database files
- `redis_data`: Redis persistence with AOF
- `app_logs`: API service logs
- `worker_logs`: Worker service logs

### Backup Data
```bash
# Backup MongoDB
docker-compose exec mongodb mongodump --db threatmesh --out /data/backup

# Backup volumes
docker run --rm -v mongodb_data:/data -v $(pwd):/backup alpine tar czf /backup/mongodb_backup.tar.gz -C /data .
```