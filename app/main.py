import os
import logging
from fastapi import FastAPI, Depends, HTTPException, status
from fastapi.security.api_key import APIKeyHeader
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv

from app.routers.asm.router import router as asm_router
from app.routers.cti.router import router as cti_router
from app.routers.tsm.router import router as tsm_router

# Load environment variables
load_dotenv()

# Configure logging
log_level = os.getenv("LOG_LEVEL", "INFO").upper()
logging.basicConfig(
    level=getattr(logging, log_level, logging.INFO),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)

# Set uvicorn loggers to the same level
uvicorn_logger = logging.getLogger("uvicorn")
uvicorn_access_logger = logging.getLogger("uvicorn.access")
uvicorn_logger.setLevel(getattr(logging, log_level, logging.INFO))
uvicorn_access_logger.setLevel(getattr(logging, log_level, logging.INFO))

logger = logging.getLogger(__name__)

# API Key authentication
API_KEY = os.getenv("API_KEY")
api_key_header = APIKeyHeader(name="X-API-Key")

def get_api_key(api_key: str = Depends(api_key_header)):
    if api_key != API_KEY:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API Key",
        )
    return api_key

# Create FastAPI app
app = FastAPI(
    title="ThreatMesh API",
    description="API for Attack Surface Mapping and Cyber Threat Intelligence",
    version="0.1.0",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

# Include routers
app.include_router(
    asm_router,
    prefix="/asm",
    tags=["Attack Surface Mapping"],
    dependencies=[Depends(get_api_key)],
)

app.include_router(
    cti_router,
    prefix="/cti",
    tags=["Cyber Threat Intelligence"],
    dependencies=[Depends(get_api_key)],
)

app.include_router(
    tsm_router,
    prefix="/tsm",
    tags=["Task Management System"],
    dependencies=[Depends(get_api_key)],
)

@app.get("/")
async def root():
    return {"message": "Welcome to ThreatMesh API"}

@app.get("/health")
async def health_check():
    """
    Health check endpoint to verify API is running and deployment was successful.
    """
    return {
        "status": "healthy",
        "message": "ThreatMesh API is running successfully",
        "service": "ThreatMesh API",
        "version": "0.1.0",
        "deployment_status": "successful"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app.main:app", host="0.0.0.0", port=8000)
