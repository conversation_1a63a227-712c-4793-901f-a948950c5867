rules:

- name: Dropbox Short-lived access token
  id: np.dropbox.1

  pattern: "\\b(sl\\.[a-zA-Z0-9_-]{130,152})(?:$|[^a-zA-Z0-9_-])"

  references:
  - https://www.dropbox.com/developers/reference/auth-types
  - https://www.dropbox.com/developers/reference/oauth-guide

  examples:
  - |
      dropbox_token = "******************************************************************************************************************************************"
  - |
      var dbx = new Dropbox.Dropbox({
        accessToken: "******************************************************************************************************************************************"
      });
