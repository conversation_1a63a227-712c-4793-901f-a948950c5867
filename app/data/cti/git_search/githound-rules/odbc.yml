rules:

- name: Credentials in ODBC Connection String
  id: np.odbc.1

  pattern: (?:User|User\ Id|UserId|Uid)\s*=\s*([^\s;]{3,100})\s*;[ \t]*.{0,10}[ \t]*(?:Password|Pwd)\s*=\s*([^\s;]{3,100})\s*(?:[;"']|$)

  examples:
  - 'Server=host;Port=5432;User Id=username;Password=******;Database=databasename;'
  - 'Server=host;Port=5432;SomeOtherKey=SomeOtherValue;User Id=username;Password=******;Database=databasename;'
  - 'Data Source=***************,1433;Network Library=DBMSSOCN;Initial Catalog=myDataBase;User ID=myUsername;Password=myPassword;'
  - 'Data Source=***************,1433;Network_library=DBMSSOCN;Initial Catalog=myDataBase;User ID=myUsername;Password=myPassword;'
  - 'Provider=SQLNCLI;Server=myServerName,myPortNumber;Database=myDataBase;Uid=myUsername;Pwd=myPassword;'
  - '    adoConn.Open("Provider=SQLOLEDB.1;User ID=specialbill_user; " & "Password =specialbill_user;Initial Catalog=SpecialBill_PROD;Data Source=uszdba01;")'
  - |
      "driver={SQL Server};server=(#{datastore['DBHOST']});database=#{datastore['DBNAME']};uid=#{datastore['DBUID']};pwd=#{datastore['DBPASSWORD']}"

  negative_examples:
  - "def login(self, user = '', password = '', domain = ''):"
  - |
      if datastore['VERBOSE']
            text  = ''
            text << "User=#{username}, "
            text << "Password=#{password}, "
            text << "Domain=#{domain}, "
            text << "Full Name=#{full_name}, "
            text << "E-mail=#{e_mail}"
            print_good(text)
  - |
      if (len < ulen + wlen + 2)
        break;
      user = (char *) (p + 1);
      pwd = (char *) (p + ulen + 2);
      p += ulen + wlen + 2;

  - |
      /* Set default values */
      server = xmalloc(sizeof(*server));
      server->user = "anonymous";
      server->password = "busybox@";

  - |
      System.out.println("Here we go...");
                String url = "************************************";
                String userid = "userid";
                String password = "password";

  - |
      char *domain = NULL;
      char *user = NULL;
      char *password = NULL;

  - |
      <?php
              \$user = \$_POST[\"username\"];
              \$pwd = \$_POST[\"password\"];
              \$otherdata = \$_POST[\"otherdata\"];
      ?>

  references:
  - https://docs.aws.amazon.com/redshift/latest/mgmt/configure-odbc-connection.html
  - https://docs.microsoft.com/en-us/azure/data-explorer/kusto/api/connection-strings/kusto
  - https://docs.microsoft.com/en-us/azure/mariadb/howto-connection-string
  - https://docs.microsoft.com/en-us/azure/mysql/single-server/how-to-connection-string
  - https://www.connectionstrings.com/
