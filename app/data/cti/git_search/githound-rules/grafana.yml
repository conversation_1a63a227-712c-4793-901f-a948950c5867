rules:

- name: Grafana API Token
  id: np.grafana.1

  pattern: \b(eyJrIjoi[A-Za-z0-9]{60,100})\b

  references:
  - https://grafana.com/docs/grafana/latest/developers/http_api/auth/

  examples:
  - 'Authorization: Bearer eyJrIjoiWHZiSWd5NzdCYUZnNUtibE8obUpESmE2bzJYNDRIc1UiLCJuIjoibXlrZXkiLCJpZCI7MX1'
  - 'admin_client = GrafanaClient("eyJrIjoiY21sM1JRYjB6RnVYSTNLenRWQkFEaWN2bXI2V202U2IiLCJuIjoiYWRtaW5rZXkiLCJpZCI6MX0=", host=grafana_host, port=3000, protocol="http")'

  non_examples:
  - 'View the latest results [here](https://heyo.powerbi.com/view?r=eyJrIjoiYTZjMTk3YjEtMzQ4Yi00NTI5LTg6ZDItNmUyMGRlOTkwMGRlIiwidCI5IjcyZjk4OGJmLTg3ZjEtNDFhZi06MWFiLTJkN2NkMDExZGI0NyIsImMiOjV9&pageName=ReportSection9567390a89a2d30b0eda).'


- name: Grafana Cloud API Token
  id: np.grafana.2

  pattern: \b(glc_eyJrIjoi[A-Za-z0-9]{60,100})\b

  references:
  - https://grafana.com/docs/grafana-cloud/api-reference/cloud-api/

  examples:
  - '  "token": "************************************************************************************************"'


- name: Grafana Service Account Token
  id: np.grafana.3

  pattern: \b(glsa_[a-zA-Z0-9]{32}_[a-fA-F0-9]{8})\b

  references:
  - https://grafana.com/docs/grafana/latest/administration/service-accounts/

  examples:
  - |
      curl -H "Authorization: Bearer glsa_HOruNAb7SOiCdshU7algkrq7FDsNSLAa_55e2f8be" -X GET '<grafana_url>/api/access-control/user/permissions' | jq

  - |
      // getData()
      // {
      //  let url="http://localhost:4200/api/search"
      //  const headers = new HttpHeaders({
      //    'Content-Type': 'application/json',
      //    'Authorization': `Bearer glsa_Sof0HKi3agxrQP9qm5r2G98VacBNwV5P_9b638c45`
      //  })
      //  return this.http.get(url, {headers: headers});
      // }
