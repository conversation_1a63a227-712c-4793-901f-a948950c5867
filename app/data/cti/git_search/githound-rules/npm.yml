rules:

- name: NPM Access Token (fine-grained)
  id: np.npm.1

  pattern: \b(npm_[A-Za-z0-9]{36})\b

  references:
  - https://docs.npmjs.com/about-access-tokens
  - https://github.com/github/roadmap/issues/557
  - https://github.blog/changelog/2022-12-06-limit-scope-of-npm-tokens-with-the-new-granular-access-tokens/

  examples:
  - '****************************************'

  # There are also NPM Legacy Access Tokens, which appear to be non-prefixed v4 UUIDs.
  # Matching these would require a pattern that uses heuristics against surrounding context.
  negative_examples:
  - '-export NPM_TOKEN="************************************"'
