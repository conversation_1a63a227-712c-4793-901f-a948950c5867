rules:

- name: Doppler CLI Token
  id: np.doppler.1

  pattern: \b(dckr_pat_[a-zA-Z0-9_-]{27})(?:$|[^a-zA-Z0-9_-])

  examples:
  - *************************************************

  references:
  - https://docs.doppler.com/reference/api
  - https://docs.doppler.com/reference/auth-token-formats

- name: Doppler Personal Token
  id: np.doppler.2

  pattern: \b(dp\.pt\.[a-zA-Z0-9]{40,44})\b

  examples:
  - *************************************************

  references:
  - https://docs.doppler.com/reference/api
  - https://docs.doppler.com/reference/auth-token-formats

- name: Doppler Service Token
  id: np.doppler.3

  pattern: \b(dp\.st\.(?:[a-z0-9\-_]{2,35}\.)?[a-zA-Z0-9]{40,44})\b

  examples:
  - *****************************************************

  references:
  - https://docs.doppler.com/reference/api
  - https://docs.doppler.com/reference/auth-token-formats

- name: Doppler Service Account Token
  id: np.doppler.4

  pattern: \b(dp\.sa\.[a-zA-Z0-9]{40,44})\b

  examples:
  - *************************************************

  references:
  - https://docs.doppler.com/reference/api
  - https://docs.doppler.com/reference/auth-token-formats

- name: Doppler SCIM Token
  id: np.doppler.5

  pattern: \b(dp\.scim\.[a-zA-Z0-9]{40,44})\b

  examples:
  - ***************************************************

  references:
  - https://docs.doppler.com/reference/api
  - https://docs.doppler.com/reference/auth-token-formats

- name: Doppler Audit Token
  id: np.doppler.6

  pattern: \b(dp\.audit\.[a-zA-Z0-9]{40,44})\b

  examples:
  - ****************************************************

  references:
  - https://docs.doppler.com/reference/api
  - https://docs.doppler.com/reference/auth-token-formats
