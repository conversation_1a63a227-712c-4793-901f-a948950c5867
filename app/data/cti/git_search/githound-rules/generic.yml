# The rules in this file detect "generic" secrets.
# Generic secrets do not have well-defined structure like most new API token schemes do.
# In particular, there isn't some notable prefix to search for, nor is there a
# fixed length and alphabet for the secret content.
# Because of all this, the patterns in these rules tend to be pretty complex, in
# an attempt to avoid false positives.
# These rules all have relatively bad precision and recall.

rules:

- name: Generic Secret
  id: np.generic.1

  pattern: (?i:secret.{0,20}\b([0-9a-z]{32,64})\b)


  examples:
  - '    private static String CLIENT_SECRET = "6fb1cff7690db9ac066cadbbde8e3c078efdabcf";'

  # FIXME: extend this rule so these examples get matched
  negative_examples:
  - "    client_credential='****************************************'"
  - "    secret_access_key = 'abcdefg12346+FJQCK'"
  - '    Ldap password               ----   H7IKC85R#@4$'


- name: Generic API Key
  id: np.generic.2

  pattern: (?i:(?:api_key|apikey|access_key|accesskey).{0,3}[\ \t]*(?:=|:=|=>|:|,|'|")[\ \t]*.{0,3}\b([0-9a-z][0-9a-z\-._/+]{30,62}[0-9a-z])\b)


  examples:
  - 'API_KEY = "951bc382db9abad29c68634761dd6e19"'
  - 'buildConfigField ''String'' , ''API_KEY'' , ''"951bc382db9cfee29c68634761dd6e19"''	API_KEY	'

  negative_examples:
  - 'name="ws_plugin__s2member_amazon_s3_comp_files_access_key" id="ws-plugin--s2member-amazon-s3-comp-files-access-key"'


- name: Generic Username and Password (quoted)
  id: np.generic.3

  pattern: (?:username|USERNAME|user|USER)[\ \t]*=[\ \t]*["']([a-zA-Z0-9.@_\-+]{3,30})["']\s*[,;]?\s*(?:\s*(?:\#|//)[^\n\r]*[\n\r])*?(?:password|pass|PASSWORD|PASS)[\ \t]*=[\ \t]*["']([^"']{5,30})["']


  examples:
  - |
      credential = UsernamePasswordCredential(
          client_id='da34859b-2ae4-48c3-bfe0-1b28b7cf2eed',
          username='donjuandemarco',
          password='1qay@WXS????',
          tenant_id='bc877b20-f135-4c13-a266-8ed26b8f0f4b')

  - |
      hostname = '***********'
      username = '<EMAIL>'
      password = '`123QWERasdf'

  - |
      hostname = '***********'
      USERNAME = '<EMAIL>'
      # some comment
      # some other comment
      PASS = '`123QWERasdf'

  - |
      user = 'abuser'  # some comment
      password = 'abuser123456'  # some other comment

  - |
      user = 'Aladdin'
      password = 'open sesame'


  negative_examples:
  - |
      USERNAME=donjuan
      PASSWORD=$($(dirname $0)/../bin/get-django-setting LOCAL_DATABASE_PASSWORD)
  - ":authn_dbd_params => 'host=db_host port=3306 user=apache password=###### dbname=apache_auth',"

  # FIXME: extend this rule so this actually gets matched
  - |
      #if DEBUG
                string backend_host = "amazon-subdomain-for-database.string.us-east-1.rds.amazonaws.com";
                string backend_user = "root";
                string backend_pass = "XXXXXXXXXXXXX";
                string backend_db = "database_db";
                string backend_port = "1234";



- name: Generic Username and Password (unquoted)
  id: np.generic.4
  pattern: (?:username|USERNAME|user|USER)[\ \t]*=[\ \t]*([a-zA-Z0-9.@_\-+]{3,30})\s*;?\s*(?:\s*(?:\#|//)[^\n\r]*[\n\r])*?(?:password|pass|PASSWORD|PASS)[\ \t]*=[\ \t]*(\S{5,30})(?:\s|$)

  examples:
  - |
      user = Aladdin
      password = open_sesame

  - |
      user = Aladdin
      // some comment
      // some other comment
      password = open_sesame

  - ":authn_dbd_params => 'host=db_host port=3306 user=apache password=###### dbname=apache_auth',"

  negative_examples:
  - |
      user = 'Aladdin'
      password = 'open_sesame'


- name: Generic Password (double quoted)
  id: np.generic.5

  pattern: (?i:password["']?[\ \t]*(?:=|:|:=|=>)[\ \t]*"([^$<%@.,\s+'"(){}&/\#\-][^\s+'"(){}/]{4,})")


  examples:
  - |
      password = "super$ecret"
  - |
      password="super$ecret"
  - |
      String usernamePassword = "application:" + appKey + ":" + appSecret;
  - |
      my_password: "super$ecret"
  - |
      "password": "super$ecret",
  - |
      my_password := "super$ecret"
  - |
      password   =>   "super$ecret"
  - |
      "ApplicationServicesConnection" : {
          "ServiceAddress" : "https://services-dev.examples.com",
          "AdminPassword" : "thisismypassword"
      }
  - |
      private const string DevFolkoosComPfxPassword = "thisismypassword";
  - |
      "password": "YOURPASSWROD"
  - |
        create_random_name('sfrp-cli-cert2', 24),
                    'cluster_name': self.create_random_name('sfrp-cli-', 24),
                    'vm_password': "Pass123!@#",
                    'policy_path': os.path.join(TEST_DIR, 'policy.json')
                })

  negative_examples:
  - |
      password = "123"
  - |
      password = super$ecret
  - |
      password = 'super$ecret'
  - |
      "password": "$super$ecret",
  - |
      sb.append("MasterUserPassword: " + getMasterUserPassword() + ",");
  - |
      "//localhost:1337/:_password = "+new Buffer("feast").toString("base64")
  - |
      export PGPASSWORD="$gdcapi_db_password"
  - |
      define wget::authfetch($source,$destination,$user,$password="",$timeout="0",$verbose=false) {
  - |
      - echo 'export DATABASE_PASSWORD="'$PRECOMPILE_PASSWORD'"' >> .env
  - |
      "/en/enterprise/3.0/authentication/keeping-your-account-and-data-secure/creating-a-strong-password":"/en/enterprise-server@3.0/auth"
  - |
      "password": "&lt;YOURPASSWROD&gt;"
  - |
      as: 'cms_user_password'
        get '/passwords/:id/edit' => "cms/sites/passwords#edit", as: 'edit_password'
        put '/forgot-password' => "cms/sites/passwords#update", as: 'update_password'
      end
  - |
      IAMUserChangePassword = "arn:aws:iam::aws:policy/IAMUserChangePassword"
  - |
      this.addPassword = "#add-password";



- name: Generic Password (single quoted)
  id: np.generic.6

  pattern: (?i:password["']?[\ \t]*(?:=|:|:=|=>)[\ \t]*'([^$<%@.,\s+'"(){}&/\#\-][^\s+'"(){}/]{4,})')


  examples:
  - |
      :password       => '4ian1234',
  - |
      common.then_log_in({username: 'geronimo', password: '52VeZqtHDCdAr5yM'});

  - |
      beta => {
        host            => 'foo.example.com',
        user            => 'joe',
        password        => 'thisismypassword',
      }

  negative_examples:
  - |
      echo 'password = '.$p['config']['daemon_password']."\n";
  - |
      usernameLabel:"Username or email:",passwordLabel:"Password:",rememberMeLabel:"Remember me:"
  - |
      this.addPassword = '#add-password';
