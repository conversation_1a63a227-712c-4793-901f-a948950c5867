rules:

- name: Twitter Client ID
  id: np.twitter.1

  pattern: \btwitter.?(?:api|app|application|client|consumer|customer)?.?(?:id|identifier|key).{0,2}\s{0,20}.{0,2}\s{0,20}.{0,2}\b([a-z0-9]{18,25})\b

  references:
  - https://developer.twitter.com/en/docs/authentication/overview

  examples:
  - '     TWITTER_ID:               "DkWLqcP3ace3wHuJ7fiw",'
  - |
      # TWITTER_API_KEY = 'UZYoBAfBzNace3mBwPOGYw'
      # TWITTER_API_SECRET = 'ngHaeaRPKA5BDQNXace3LWA1PvTA1kBGDaAJmc517E'


- name: Twitter Secret Key
  id: np.twitter.2

  pattern: twitter.?(?:api|app|application|client|consumer|customer|secret|key)?.?(?:key|oauth|sec|secret)?.{0,2}\s{0,20}.{0,2}\s{0,20}.{0,2}\b([a-z0-9]{35,44})\b

  references:
  - https://developer.twitter.com/en/docs/authentication/overview

  examples:
  - |
      # TWITTER_API_KEY = 'UZYoBAfBzNace3mBwPOGYw'
      # TWITTER_API_SECRET = 'ngHaeaRPKA5BDQNXace3LWA1PvTA1kBGDaAJmc517E'

  # XXX It would be nice if this actually matched
  negative_examples:
  - |
      Twitter(auth=OAuth('MjuHWoGbzYmJv3ZuHaBvSENfyevu00NQuBc40VM',
                         'anJLBCOALCXl7aXeybmNA5oae9E03Cm23cKNMLaScuXwk',
                         'kl3E14NQx84qxO1dy247V0b2W',
                         '5VFVXVMq9bDJzFAKPfWOiYmJZin2F7YLhSfoyLBXf6Bc9ngX3g'))
