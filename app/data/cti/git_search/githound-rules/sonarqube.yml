rules:

- name: SonarQube Token
  id: np.sonarqube.1

  pattern: '(?i)sonar.{0,5}login.{0,5}\s*\b([a-f0-9]{40})\b'

  references:
  - https://docs.sonarqube.org/latest/user-guide/user-token/

  examples:
  - 'sonar.host.url=https://sonarcloud.io -Dsonar.login=****************************************'
  - "sonar.login', '****************************************"
  - '$sonarLogin = "****************************************"'

  negative_examples:
  - 'sonarqube-reporter-1.2.4.tgz#****************************************'
  - 'sonarqube-reporter-1.4.0.tgz#****************************************'
  - 'sonarqube-scanner/-/sonarqube-scanner-2.5.0.tgz#****************************************'
  - |
      /d:sonar.host.url=$(SONAR_HOST) /d:sonar.login=$(SONAR_LOGIN) \
      /d:sonar.coverage.exclusions="**Tests*.cs"
