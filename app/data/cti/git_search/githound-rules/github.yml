rules:

- name: GitHub Personal Access Token
  id: np.github.1
  pattern: '\b(ghp_[a-zA-Z0-9]{36})\b'

  references:
  - https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/about-authentication-to-github
  - https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/creating-a-personal-access-token
  - https://github.blog/2021-04-05-behind-githubs-new-authentication-token-formats/

  examples:
  - 'GITHUB_KEY=****************************************'
  - "let g:gh_token='****************************************'"
  - |
      ## git devaloper settings
      ****************************************


- name: GitHub OAuth Access Token
  id: np.github.2
  pattern: '\b(gho_[a-zA-Z0-9]{36})\b'

  references:
  - https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/about-authentication-to-github
  - https://docs.github.com/en/developers/apps/building-oauth-apps/authorizing-oauth-apps
  - https://github.blog/2021-04-05-behind-githubs-new-authentication-token-formats/

  examples:
  - '    "url": "git+https://FelipeMestre:<EMAIL>/gontarz/PW_2021_Website-FelipeMestre.git"'
  - '    oauth_token: ****************************************'


- name: GitHub App Token
  id: np.github.3
  # Note: `ghu_` prefix is for user-to-server tokens; `ghs_` is for server-to-server tokens
  pattern: '\b((?:ghu|ghs)_[a-zA-Z0-9]{36})\b'

  references:
  - https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/about-authentication-to-github
  - https://docs.github.com/en/developers/apps/building-oauth-apps/authorizing-oauth-apps
  - https://github.blog/2021-04-05-behind-githubs-new-authentication-token-formats/

  examples:
  - '            "token": "****************************************",'
  - |
      Example usage:
      git clone http://<EMAIL>/username/repo.git


- name: GitHub Refresh Token
  id: np.github.4
  pattern: '\b(ghr_[a-zA-Z0-9]{76})\b'
  references:
  - https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/about-authentication-to-github
  - https://docs.github.com/en/developers/apps/building-oauth-apps/authorizing-oauth-apps
  - https://github.blog/2021-04-05-behind-githubs-new-authentication-token-formats/

  examples:
  - '    "refresh_token": "********************************************************************************",'


- name: GitHub Client ID
  id: np.github.5
  pattern: (?i:(?:github).?(?:api|app|application|client|consumer|customer)?.?(?:id|identifier|key).{0,2}\s{0,20}.{0,2}\s{0,20}.{0,2}\b([a-z0-9]{20})\b)


  references:
  - https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/about-authentication-to-github
  - https://docs.github.com/en/developers/apps/building-oauth-apps/authorizing-oauth-apps

  examples:
  - |
      GITHUB_CLIENT_ID=********************
      GITHUB_SECRET=****************************************


- name: GitHub Secret Key
  id: np.github.6
  pattern: (?i:github.?(?:api|app|application|client|consumer|customer|secret|key).?(?:key|oauth|sec|secret)?.{0,2}\s{0,20}.{0,2}\s{0,20}.{0,2}\b([a-z0-9]{40})\b)

  references:
  - https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/about-authentication-to-github
  - https://docs.github.com/en/developers/apps/building-oauth-apps/authorizing-oauth-apps

  examples:
  - |
      GITHUB_CLIENT_ID=********************
      GITHUB_SECRET=****************************************


- name: GitHub Personal Access Token (fine-grained permissions)
  id: np.github.7
  pattern: \b(github_pat_[0-9a-zA-Z_]{82})\b


  references:
  - https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/about-authentication-to-github
  - https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/creating-a-personal-access-token

  examples:
  - '*********************************************************************************************'
