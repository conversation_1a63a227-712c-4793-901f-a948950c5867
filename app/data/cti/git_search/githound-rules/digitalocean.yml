rules:

- name: DigitalOcean Application Access Token
  id: np.digitalocean.1

  pattern: \b(doo_v1_[a-f0-9]{64})\b

  references:
  - https://docs.digitalocean.com/reference/api/

  examples:
  - 'curl -X GET -H "Content-Type: application/json" -H "Authorization: Bearer ***********************************************************************" "https://api.digitalocean.com/v2/cdn/endpoints"'


- name: DigitalOcean Personal Access Token
  id: np.digitalocean.2

  pattern: \b(dop_v1_[a-f0-9]{64})\b

  references:
  - https://docs.digitalocean.com/reference/api/

  examples:
  - 'token         = "***********************************************************************"'


- name: DigitalOcean Refresh Token
  id: np.digitalocean.3

  pattern: \b(dor_v1_[a-f0-9]{64})\b

  references:
  - https://docs.digitalocean.com/reference/api/

  examples:
  - '  "refresh_token": "***********************************************************************",'
