rules:

- name: PyPI Upload Token
  id: np.pypi.1

  # NOTE: these can actually be arbitrarily long
  pattern: \b(pypi-AgEIcHlwaS5vcmc[a-zA-Z0-9_-]{50,})(?:[^a-zA-Z0-9_-]|$)

  references:
  # GitHub Secrets Scanning implementation issue and discussion
  - https://github.com/pypa/warehouse/issues/6051
  # A library that generates PyPI tokens (which are b64-encoded macaroons)
  - https://pypi.org/project/pypitoken/
  # The library that PyPi uses in its backend?
  - https://github.com/ecordell/pymacaroons
  - https://en.wikipedia.org/wiki/Macaroons_(computer_science)
  - https://github.com/pypa/warehouse/blob/82815b06d9f98deed5f205c66e054de59d22a10d/docs/development/token-scanning.rst
  - https://research.google/pubs/pub41892/

  examples:
  - '# password = ****************************************************************************************************************************************************************************'
  - |
      - name: Publish package
      uses: pypa/gh-action-pypi-publish@27b31702a0e7fc50959f5ad993c78deac1bdfc29
      with:
        user: santoshp
        password: ${{ secrets.***********************************************************************************************************************************************************************************DKqoXNH4_rMkO5SQYItA}}
  - 'password: ***********************************************************************************************************************************************************************************8cIBQt7HckwM4G3q-462xphsLbm7IZvjqMS4jvQw'
