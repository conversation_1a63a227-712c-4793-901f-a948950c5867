rules:

# This is intended to detect hardcoded credentials that sometimes appear in Gradle files.
- name: Hardcoded Gradle Credentials
  id: np.gradle.1

  pattern: credentials\s*\{[\s\S]*?(?:username|password)\s+['"]([^'"]{1,60})['"][\s\S]*?(?:username|password)\s+['"]([^'"]{1,60})['"]

  examples:
  - |
      credentials {
          username 'user'
          password 'password'
      }
  - |
      publishing {
        repositories {
            maven {
                url "http://us01cmsysart01.example.com:8081/artifactory/Mobile-Libs-Internal"
                credentials {
                    // your password here

                    username "SOME_USERNAME"
                    password "SOME_PASSWORD"
                }
            }
        }
  - "credentials {\n  username 'user'\n  password 'password'\n}"
  - "credentials {\n  username \"user\"\n  password \"password\"\n}"
