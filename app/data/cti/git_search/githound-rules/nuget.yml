rules:

- name: NuGet API Key
  id: np.nuget.1

  pattern: '\b(oy2[a-z0-9]{43})\b'

  references:
  - https://docs.microsoft.com/en-us/nuget/nuget-org/publish-a-package#create-api-keys

  examples:
  - 'nuget push %filename% ********************************************** -Source https://api.nuget.org/v3/index.json'
  - 'find . -name "*.nupkg"|xargs -I {} dotnet nuget push "{}"  --api-key  **********************************************  -s https://api.nuget.org/v3/index.json --skip-duplicate'
