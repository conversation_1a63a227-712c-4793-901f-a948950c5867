rules:

- name: Azure Connection String
  id: np.azure.1

  # XXX There are a bunch of other keys that seem to have secret content assigned to them:
  #
  #   - SharedAccessSignature
  #   - Authorization
  #   - UserToken
  #   - ApplicationToken
  #
  # Maybe we can generalize this rule one day.
  pattern: (?i:(?:AccountName|SharedAccessKeyName|SharedSecretIssuer)\s*=\s*([^;]{1,80})\s*;\s*.{0,10}\s*(?:AccountKey|SharedAccessKey)\s*=\s*([^;]{1,80}))

  references:
  - https://azure.microsoft.com/en-us/blog/windows-azure-web-sites-how-application-strings-and-connection-strings-work/
  - https://docs.microsoft.com/en-us/azure/storage/common/storage-configure-connection-string
  - https://learn.microsoft.com/en-us/azure/service-bus-messaging/service-bus-sas#best-practices-when-using-sas

  examples:
  - |
      # Azure Storage Connection String
      AzureWebJobsStorage=DefaultEndpointsProtocol=https;AccountName=hanatour9833;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net
  - |
      DefaultEndpointsProtocol=http;AccountName=testacc1;
      AccountKey=1gy3lpE7Du1j5ljKiupgKzywSw2isjsa69sfsdfsdsgfsgfdgfdgfd/YThisv/OVVLfIOv9kQ==;
      BlobEndpoint=http://127.0.0.1:8440/testacc1;
      TableEndpoint=http://127.0.0.1:8440/testacc1;
      QueueEndpoint=http://127.0.0.1:8440/testacc1;

  - |
      "IOTHUB_CONNECTION_STRING": {
        "value": "HostName=d1-vi-ioth521.azure-devices.net;SharedAccessKeyName=registryReadWrite;SharedAccessKey=S8ii67l3Gd1Ba69az78iP9UksewzhjvUfh1DIuDs30w="
      }

  - |
      "AZURE_STORAGE_CONNECTION_STRING": {
        "value": "DefaultEndpointsProtocol=https;AccountName=d1biblobstor521;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"
      }

  negative_examples:
  # https://docs.microsoft.com/en-us/azure/azure-monitor/app/sdk-connection-string
  - 'InstrumentationKey=********-0000-0000-0000-********0000;EndpointSuffix=ai.contoso.com;'
  - 'InstrumentationKey=********-0000-0000-0000-********0000;IngestionEndpoint=https://custom.com:111/;LiveEndpoint=https://custom.com:222/;ProfilerEndpoint=https://custom.com:333/;SnapshotEndpoint=https://custom.com:444/;'


- name: Azure App Configuration Connection String
  id: np.azure.2

  pattern: (https://[a-zA-Z0-9-]+\.azconfig\.io);Id=(.{4}-.{2}-.{2}:[a-zA-Z0-9+/]{18,22});Secret=([a-zA-Z0-9+/]{36,50}=)

  examples:
  - 'Endpoint=https://foo-nonprod-appconfig.azconfig.io;Id=ABCD-E6-s0:tl6ABcdefGHi7kLMno/p;Secret=abCD1EF+GHIJxLMnOPqRSa53VWX05zaBCdE/fg9hi4k='
  - 'https://foo-nonprod-appconfig.azconfig.io;Id=ABCD-E6-s0:tl6ABcdefGHi7kLMno/p;Secret=abCD1EF+GHIJxLMnOA53ST8uVWX05zaBCdE/fg9hi4k='
  - 'Endpoint=https://appconfig-test01.azconfig.io;Id=09pv-l0-s0:opFCQMC6+9485xJgN5Ws;Secret=GcoEA53t7GLRNJ910M46IrbHO/Vg0tt4HujRdsaCoTY='
  - '        private static string appConfigurationConnectionString = "Endpoint=https://appcs-fg-pwc.azconfig.io;Id=pi5x-l9-s0:SZLlhHA53Nz2MpAl04cU;Secret=CQ+mlfQqkzfZv4XA53gigJ/seeXMKwNsqW/rM3wmtuE=";'

  negative_examples:
  - |
          text:
            az appconfig feature delete --connection-string Endpoint=https://contoso.azconfig.io;Id=***;Secret=*** --feature color --label MyLabel

  references:
  - https://docs.microsoft.com/en-us/azure/azure-app-configuration/
  - https://docs.microsoft.com/en-us/azure/azure-app-configuration/howto-best-practices
  - https://github.com/Azure/azure-sdk-for-python/blob/main/sdk/appconfiguration/azure-appconfiguration/azure/appconfiguration/_utils.py
