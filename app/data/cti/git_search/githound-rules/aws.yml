rules:

- name: AWS API Key
  id: np.aws.1

  pattern: '\b((?:A3T[A-Z0-9]|AKIA|AGPA|AIDA|AROA|AIPA|ANPA|ANVA|ASIA)[A-Z0-9]{16})\b'

  references:
  - https://docs.aws.amazon.com/IAM/latest/UserGuide/best-practices.html
  - https://docs.aws.amazon.com/IAM/latest/UserGuide/id_credentials_access-keys.html
  - https://docs.aws.amazon.com/IAM/latest/UserGuide/reference_identifiers.html
  - https://docs.aws.amazon.com/accounts/latest/reference/credentials-access-keys-best-practices.html

  examples:
  - 'A3T0ABCDEFGHIJKLMNOP'
  - 'AKIADEADBEEFDEADBEEF'

  negative_examples:
  - 'A3T0ABCDEFGHIJKLMNO'
  - 'A3T0ABCDEFGHIjklmnop'
  - '======================'
  - '//////////////////////'
  - '++++++++++++++++++++++'


- name: AWS Secret Access Key
  id: np.aws.2

  pattern: "(?i)\\baws_?(?:secret)?_?(?:access)?_?(?:key)?[\"'']?\\s{0,30}(?::|=>|=)\\s{0,30}[\"'']?([a-z0-9/+=]{40})\\b"

  references:
  - https://docs.aws.amazon.com/IAM/latest/UserGuide/best-practices.html
  - https://docs.aws.amazon.com/IAM/latest/UserGuide/id_credentials_access-keys.html
  - https://docs.aws.amazon.com/IAM/latest/UserGuide/reference_identifiers.html
  - https://docs.aws.amazon.com/accounts/latest/reference/credentials-access-keys-best-practices.html

  examples:
  - 'aws_secret_access_key:aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa'
  - 'aws_secret_access_key => aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa'

  negative_examples:
  - 'export AWS_SECRET_ACCESS_KEY=ded7db27a4558e2ea9bbf0bf36ae0e8521618f366c'
  - '"aws_secret_access_key" =  aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaend'
  - '"aws_secret_access_key" =  aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaendbbbbbbb'
  - '"aws_sEcReT_key"  =  aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaend'
  # FIXME: modify the pattern to detect cases like the following
  - 'aws_secret_key: OOzkR1+hF+1ABCsIFDJMEUtqmtnZ1234567890'
  - '======================'
  - '//////////////////////'
  - '++++++++++++++++++++++'


- name: AWS Account ID
  id: np.aws.3

  pattern: '(?i)aws_?(?:account)_?(?:id)?["''`]?\s{0,30}(?::|=>|=)\s{0,30}["''`]?([0-9]{4}-?[0-9]{4}-?[0-9]{4})'

  examples:
  - |
      KeyMetadata: {
          AWSAccountId: "************",
          Arn: "arn:aws:kms:us-east-2:************:key/54348bc1-6e3b-4cda-8b18-c6033ca7d328",
          CreationDate: 2019-07-12 18:23:13 +0000 UTC,
          Description: "",
          Enabled: true,
          KeyId: "54348bc1-6e3b-4cda-8b18-c6033ca7d328",
          KeyManager: "CUSTOMER",
          KeyState: "Enabled",
          KeyUsage: "ENCRYPT_DECRYPT",
          Origin: "AWS_KMS"
      }
  - |
      4. login into ecr

      ```bash
      aws_region=eu-central-1
      aws_account_id=************
      aws_profile=serverless-bert

      aws ecr get-login-password \
          --region $aws_region \
          --profile $aws_profile \
      | docker login \
          --username AWS \
          --password-stdin $aws_account_id.dkr.ecr.$aws_region.amazonaws.com
      ```

  negative_examples:
  - '======================'
  - '//////////////////////'
  - '++++++++++++++++++++++'


- name: AWS Session Token
  id: np.aws.4
  pattern: '(?i)(?:aws.?session|aws.?session.?token|aws.?token)["''`]?\s{0,30}(?::|=>|=)\s{0,30}["''`]?([a-z0-9/+=]{16,200})[^a-z0-9/+=]'

  negative_examples:
  - '======================'
  - '//////////////////////'
  - '++++++++++++++++++++++'

  examples:
  - |
      export AWS_ACCESS_KEY_ID="I08BCX2ACV45ED1DOC9J"
      export AWS_SECRET_ACCESS_KEY="0qk+o7XctJMmG6ydO8537c9+TofLJU1K0PiVBXSg"
      export AWS_SESSION_TOKEN="eyJhbGciOiJIUzUxMi53InR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.J-a9PORJToz7MUrnPQlOywcqtVMNkXy53Gedp_V4PW-Gbf1_BAMjwuw_X7fKRd6hkNfEn43CKKju7muzi_d1Ig"


# Note that this service is being deprecated on March 31, 2024
- name: Amazon MWS Auth Token
  id: np.aws.5
  pattern: '(?i)(amzn\.mws\.[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})'

  examples:
  - |
      AWS Access Key:********************
      secret key:IwJz1SHMccAKUKuskdVoHFfkre73BTyF80nRmcWc
      MWS Authorisation Token:  amzn.mws.dab428a1-ed97-fd8d-e045-950d712f6f58

  references:
  - https://docs.developer.amazonservices.com/en_US/dev_guide/index.html


# - name: AWS S3 Bucket (subdomain style)
#   id: np.s3.1

#   pattern: |
#     (?x)
#     (?: ^ | [\s/"'] | %2F )
#     (
#     (?: [a-zA-Z0-9_-]+ \. )+   (?# bucket name as subdomain )
#     (?: s3
#       | s3-af-south-1
#       | s3-ap-east-1
#       | s3-ap-northeast-1
#       | s3-ap-northeast-2
#       | s3-ap-northeast-3
#       | s3-ap-south-1
#       | s3-ap-south-2
#       | s3-ap-southeast-1
#       | s3-ap-southeast-2
#       | s3-ap-southeast-3
#       | s3-ap-southeast-4
#       | s3-ca-central-1
#       | s3-eu-central-1
#       | s3-eu-central-2
#       | s3-eu-north-1
#       | s3-eu-south-1
#       | s3-eu-south-2
#       | s3-eu-west-1
#       | s3-eu-west-2
#       | s3-eu-west-3
#       | s3-me-central-1
#       | s3-me-south-1
#       | s3-sa-east-1
#       | s3-us-east-1
#       | s3-us-east-2
#       | s3-us-gov-east-1
#       | s3-us-gov-west-1
#       | s3-us-west-1
#       | s3-us-west-2
#       )
#     \.amazonaws\.com
#     ) \b

#   references:
#   - https://docs.aws.amazon.com/general/latest/gr/rande.html

#   examples:
#   - 'example-bucket.s3.amazonaws.com'
#   - 'http://bucket.s3-us-east-2.amazonaws.com'
#   - 'http%2F%2Fsome-bucket.s3.amazonaws.com'

#   negative_examples:
#   - '.s3.amazonaws.com'
#   - 's3.amazonaws.com'


# - name: AWS S3 Bucket (path style)
#   id: np.s3.2

#   pattern: |
#     (?x)
#     (?: ^ | [\s/"'] | %2F )
#     (
#     (?: s3
#       | s3-af-south-1
#       | s3-ap-east-1
#       | s3-ap-northeast-1
#       | s3-ap-northeast-2
#       | s3-ap-northeast-3
#       | s3-ap-south-1
#       | s3-ap-south-2
#       | s3-ap-southeast-1
#       | s3-ap-southeast-2
#       | s3-ap-southeast-3
#       | s3-ap-southeast-4
#       | s3-ca-central-1
#       | s3-eu-central-1
#       | s3-eu-central-2
#       | s3-eu-north-1
#       | s3-eu-south-1
#       | s3-eu-south-2
#       | s3-eu-west-1
#       | s3-eu-west-2
#       | s3-eu-west-3
#       | s3-me-central-1
#       | s3-me-south-1
#       | s3-sa-east-1
#       | s3-us-east-1
#       | s3-us-east-2
#       | s3-us-gov-east-1
#       | s3-us-gov-west-1
#       | s3-us-west-1
#       | s3-us-west-2
#       )
#     \.amazonaws\.com
#     /
#     [a-zA-Z0-9_][a-zA-Z0-9_-]* (?: \. [a-zA-Z0-9_-]+)*   (?# bucket name as path )
#     )
#     (?: [^a-zA-Z0-9_-] | $ )                             (?# this instead of a \b anchor because that doesn't play nicely with `-` )

#   references:
#   - https://docs.aws.amazon.com/general/latest/gr/rande.html

#   examples:
#   - 's3.amazonaws.com/example-bucket'
#   - 'http://s3-us-east-2.amazonaws.com/example-bucket'

#   negative_examples:
#   - '.s3.amazonaws.com'
#   - 's3.amazonaws.com'
#   - 's3.amazonaws.com/'
#   - 'some-bucket-name.s3.amazonaws.com/171ea24dd241f8a2178b0374-username-Reponame-3-0'
#   - 'some-bucket.s3.amazonaws.com/some-object-here'


# - name: Amazon Resource Name
#   id: np.arn.1

#   pattern: |
#     (?x)
#     \b
#     (
#       arn
#       :
#       (?: aws | aws-cn | aws-us-gov )         (?# partition )
#       :
#       [a-zA-Z0-9_-]{2,}                       (?# service )
#       :
#       (?: af-south-1
#         | ap-east-1
#         | ap-northeast-1
#         | ap-northeast-2
#         | ap-northeast-3
#         | ap-south-1
#         | ap-south-2
#         | ap-southeast-1
#         | ap-southeast-2
#         | ap-southeast-3
#         | ap-southeast-4
#         | ca-central-1
#         | eu-central-1
#         | eu-central-2
#         | eu-north-1
#         | eu-south-1
#         | eu-south-2
#         | eu-west-1
#         | eu-west-2
#         | eu-west-3
#         | me-central-1
#         | me-south-1
#         | sa-east-1
#         | us-east-1
#         | us-east-2
#         | us-gov-east-1
#         | us-gov-west-1
#         | us-west-1
#         | us-west-2
#         )?                                    (?# region )
#       :
#       (?: \d{12} )?                           (?# account ID sans hyphens )
#       :
#       (?: [a-zA-Z0-9_-]+ [:/])?               (?# resource type)
#       [^\s"'&<>\\%]+                          (?# resource ID)
#     )
#     (?: [\s"'&<>\\%] | $ )

#   references:
#   - https://docs.aws.amazon.com/IAM/latest/UserGuide/reference-arns.html

#   examples:
#   - 'arn:aws:s3:::my_corporate_bucket/*'
#   - 'arn:aws:s3:::my_corporate_bucket/Development/*'
#   - 'arn:aws:iam::************:user/Development/product_1234/*'
#   - 'alerts: "arn:aws:sns:us-west-2:************:CloudwatchMetricAlarm"'
#   - '"Principal":{"Federated":["arn:aws:iam:::oidc-provider/localhost:8080/auth/realms/quickstart"]},'
#   - '"KeyId": "arn:aws:kms:us-west-2:************:key/1234abcd-12ab-34cd-56ef-1234567890ab",'
#   - '"aws-kms://arn:aws:kms:us-east-1:************:key/84a66985-f968-4bac-82c2-365518adf157";'
#   - 'return f"arn:aws:s3:::{bucket_name}"'
#   - 'return f"arn:aws:s3:::${bucket_name}"'
