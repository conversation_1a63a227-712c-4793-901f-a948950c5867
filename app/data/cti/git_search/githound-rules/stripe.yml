rules:

# FIXME: also add an entry for stripe "Publishable Keys"? Those are public, but having it along with the secret key would be problematic.
# Example: STRIPE_PUBLISHABLE_KEY=pk_test_aQbfVWeaCesES5FRSY7iIjk9

- name: Stripe API Key
  id: np.stripe.1

  pattern: '(?i)\b((?:sk|rk)_live_[a-z0-9]{24})\b'

  references:
  - https://stripe.com/docs/keys

  examples:
  - 'Stripe.api_key = "********************************"'
  - 'var stripe = require("stripe")("********************************");'

- name: Stripe API Test Key
  id: np.stripe.2

  pattern: '(?i)\b((?:sk|rk)_test_[a-z0-9]{24})\b'

  references:
  - https://stripe.com/docs/keys

  examples:
  - '//var stripe = require("stripe")("sk_test_nxOdTTuEace5Ajbh3svpG32m");'
