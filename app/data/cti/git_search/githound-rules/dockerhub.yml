rules:

- name: Docker Hub Personal Access Token
  id: np.dockerhub.1

  pattern: \b(dckr_pat_[a-zA-Z0-9_-]{27})(?:$|[^a-zA-Z0-9_-])

  examples:
  - docker login -u gemesa -p ************************************
  - docker login -u gemesa -p ************************************
  - docker login -u gemesa -p ************************************

  references:
  - https://docs.docker.com/security/for-developers/access-tokens/
