rules:

- name: Twilio API Key
  id: np.twilio.1

  pattern: '(?i)twilio.{0,20}\b(sk[a-f0-9]{32})\b'

  examples:
  - |
      const twilioAccountSid = 'AC712594f590c0d8ace55c04858f7398f9' // Your Account SID from www.twilio.com/console
      const twilioApiKeySID = '**********************************'
      const twilioApiKeySecret = 'l6LUelKF2BUtMLace5oShZSmRppadYqI'
  - |
      // https://www.twilio.com/console/video/dev-tools/api-keys
      'API'              => env('TWILIO_API','**********************************'),
      'API_KEY'          => env('TWILIO_API_KEY', 'wbTs1SUt6Aace5eKeNCxuYvJa6PhaRd0')

  references:
  - https://www.twilio.com/docs/usage/api
  - https://www.twilio.com/docs/usage/api#authenticate-with-http
  - https://www.twilio.com/docs/usage/api#authenticate-using-the-twilio-sdks
