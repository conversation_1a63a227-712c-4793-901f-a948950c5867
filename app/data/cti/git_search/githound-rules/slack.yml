rules:

# XXX what are these?
# pattern: '\b(xoxa-[0-9]{12}-[0-9]{12}-[a-f0-9]{32})\b'
# pattern: '\b(xoxa-[0-9]{12}-[0-9]{12}-[0-9]{12}-[a-f0-9]{32})\b'
# pattern: '\b(xoxr-[0-9]{12}-[0-9]{12}-[a-z0-9]{24})\b'

- name: Slack Bot Token
  id: np.slack.2
  pattern: '\b(xoxb-[0-9]{12}-[0-9]{12}-[a-zA-Z0-9]{24})\b'

  references:
  - https://api.slack.com/authentication
  - https://api.slack.com/authentication/best-practices
  - https://api.slack.com/authentication/token-types

  examples:
  - 'SLACK_API_TOKEN=*******************************************************'

  negative_examples:
  - 'python log_announce.py **************************************************************************** "This is a test run. Ignore"'
  - |
      this is the api token to connect to the bot user

      xoxb-153445930147-Tjy11gGxUW6Cf99YOYwtzG0K
  - |
      def send_slack_notification(message):
          token = "xoxb-47834520726-N3otsrwj8Cf99cs8GhiRZsX1"


- name: Slack User Token
  id: np.slack.4
  pattern: '\b(xoxp-[0-9]{12}-[0-9]{12}-[0-9]{12}-[a-f0-9]{32})\b'

  references:
  - https://api.slack.com/authentication
  - https://api.slack.com/authentication/best-practices
  - https://api.slack.com/authentication/token-types

  examples:
  - 'python log_announce.py **************************************************************************** "This is a test run. Ignore"'
  - 'curl -X POST -H "Content-type: application/json" -H "Authorization: Bearer ****************************************************************************" --data @data.json https://wirecard-issuing.slack.com/api/chat.postMessage'
  - '	url := "https://slack.com/api/channels.history?token=****************************************************************************&channel=C4D8D3XMX&count=1&pretty=1"'

  negative_examples:
  - |
      this is the api token to connect to the bot user

      xoxb-153445930147-Tjy11gGxUW6Cf99YOYwtzG0K
  - 'SLACK_API_TOKEN=*******************************************************'
  - |
      def send_slack_notification(message):
          token = "xoxb-47834520726-N3otsrwj8Cf99cs8GhiRZsX1"


- name: Slack App Token
  id: np.slack.5
  pattern: '\b(xapp-[0-9]{12}-[a-zA-Z0-9/+]{24})\b'

  references:
  - https://api.slack.com/authentication
  - https://api.slack.com/authentication/best-practices
  - https://api.slack.com/authentication/token-types

  examples:
  - 'ENV SLACK_TOKEN="xapp-083452001657-ShAYwge/87H4lC3j7lZ48pAL" \'

- name: Slack Legacy Bot Token
  id: np.slack.6
  pattern: '\b(xoxb-[0-9]{10,13}-[a-zA-Z0-9]{24})\b'

  references:
  - https://api.slack.com/authentication
  - https://api.slack.com/authentication/best-practices
  - https://api.slack.com/authentication/token-types
  - https://api.slack.com/legacy/custom-integrations/legacy-tokens

  examples:
  - |
      this is the api token to connect to the bot user

      xoxb-153445930147-Tjy11gGxUW6Cf99YOYwtzG0K
  - |
      def send_slack_notification(message):
          token = "xoxb-47834520726-N3otsrwj8Cf99cs8GhiRZsX1"

  negative_examples:
  - 'SLACK_API_TOKEN=*******************************************************'
  - 'python log_announce.py **************************************************************************** "This is a test run. Ignore"'
  - 'curl -X POST -H "Content-type: application/json" -H "Authorization: Bearer ****************************************************************************" --data @data.json https://wirecard-issuing.slack.com/api/chat.postMessage'
  - '	url := "https://slack.com/api/channels.history?token=****************************************************************************&channel=C4D8D3XMX&count=1&pretty=1"'


- name: Slack Webhook
  id: np.slack.3
  pattern: '(?i)(https://hooks.slack.com/services/T[a-z0-9_]{8}/B[a-z0-9_]{8,12}/[a-z0-9_]{24})'

  references:
  - https://api.slack.com/messaging/webhooks

  examples:
  - '#notifications_marcus: *****************************************************************************'
  - |
      // Import and Configure Console.Slack (Thanks David <3)
      // const slack = require('console-slack');
      // slack.options = {
      //   webhook : "*****************************************************************************",
      //   username: "console.slack.bot",
      //   emoji : ":trollface:",
      //   channel : "#payx-logs"
      // };
