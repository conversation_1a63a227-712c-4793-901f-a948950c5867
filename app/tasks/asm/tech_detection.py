"""
Technology detection task.
"""
import logging
import json
import subprocess
from datetime import datetime
from typing import List, Dict, Any
from urllib.parse import urlparse

from app.db import operations
from app.models.task import TaskStatus
from app.tasks.celery_app import celery_app

logger = logging.getLogger(__name__)

@celery_app.task(bind=True)
def run_tech_detection(self, domain: str = None, urls: List[str] = None, job_id: str = None):
    """Run technology detection on URLs using Wappalyzer"""
    try:
        # Job ID should always be provided by the router
        if not job_id:
            logger.error("No job_id provided to tech detection task")
            return {"success": False, "message": "No job_id provided"}

        # Mark as started in database and store Celery task ID
        operations.update_operation(
            job_id=job_id,
            status=TaskStatus.RUNNING,
            start_time=datetime.now(),
            task_id=self.request.id  # Store Celery's task ID
        )

        # Update task state in Celery
        self.update_state(state='STARTED', meta={'progress': 0, 'message': 'Starting technology detection'})

        # Get URLs list
        if domain and not urls:
            # Get URLs from previous webserver discovery results
            urls = _get_urls_from_db(domain)
            if not urls:
                error_msg = f"No webserver discovery results found for domain {domain}. Please run webserver discovery first."
                operations.update_operation(
                    job_id=job_id,
                    status=TaskStatus.FAILED,
                    error=error_msg,
                    end_time=datetime.now()
                )
                return {"job_id": job_id, "success": False, "message": error_msg}

        if not urls:
            error_msg = "No URLs provided for technology detection"
            operations.update_operation(
                job_id=job_id,
                status=TaskStatus.FAILED,
                error=error_msg,
                end_time=datetime.now()
            )
            return {"job_id": job_id, "success": False, "message": error_msg}

        # Update progress
        self.update_state(state='STARTED', meta={'progress': 10, 'message': f'Found {len(urls)} URLs to analyze'})

        # Process each URL
        tech_detections = []
        successful_detections = 0
        failed_detections = 0

        for i, url in enumerate(urls):
            try:
                # Update progress for each URL
                progress = 10 + (80 * (i + 1) / len(urls))
                self.update_state(state='STARTED', meta={
                    'progress': int(progress), 
                    'message': f'Analyzing {url} ({i+1}/{len(urls)})'
                })
                
                # Run Wappalyzer
                tech_data = _run_wappalyzer(url)
                
                if tech_data and tech_data.get("technologies"):
                    # Extract host from URL
                    parsed_url = urlparse(url)
                    host = parsed_url.netloc or parsed_url.path.split('/')[0]
                    
                    # Create complete tech detection info with full Wappalyzer data
                    tech_info = {
                        "url": url,
                        "host": host,
                        "http_status_code": tech_data.get("urls", {}).get(url, {}).get("status", "unknown"),
                        "technologies": tech_data.get("technologies", []),
                        "tech_count": len(tech_data.get("technologies", []))
                    }
                    
                    tech_detections.append(tech_info)
                    successful_detections += 1
                else:
                    # Even if tech detection failed, include the URL in results
                    parsed_url = urlparse(url)
                    host = parsed_url.netloc or parsed_url.path.split('/')[0]
                    
                    tech_info = {
                        "url": url,
                        "host": host,
                        "http_status_code": "failed",
                        "technologies": [],
                        "tech_count": 0,
                        "error": "Technology detection failed or no technologies detected"
                    }
                    
                    tech_detections.append(tech_info)
                    failed_detections += 1
                    
            except Exception as e:
                logger.warning(f"Technology detection failed for {url}: {e}")
                
                # Include failed URL in results
                parsed_url = urlparse(url)
                host = parsed_url.netloc or parsed_url.path.split('/')[0]
                
                tech_info = {
                    "url": url,
                    "host": host,
                    "status": "error",
                    "technologies": [],
                    "tech_count": 0,
                    "error": str(e)
                }
                
                tech_detections.append(tech_info)
                failed_detections += 1
                continue

        # Update progress
        self.update_state(state='STARTED', meta={'progress': 90, 'message': 'Finalizing results'})

        # Collect all detected technologies (just slugs for statistics)
        all_tech_slugs = set()
        for detection in tech_detections:
            for tech in detection.get("technologies", []):
                if tech.get("slug"):
                    all_tech_slugs.add(tech["slug"])

        # Store tech detection results in chunks for pagination
        from app.db.operations import store_tech_detection_chunk
        
        chunk_size = 50
        chunk_number = 0
        
        for i in range(0, len(tech_detections), chunk_size):
            chunk_data = tech_detections[i:i + chunk_size]
            store_tech_detection_chunk(job_id, chunk_number, chunk_data)
            chunk_number += 1

        # Prepare structured output with statistics (detailed data is in chunks)
        structured_output = {
            "statistics": {
                "total": len(tech_detections),
                "total_chunks": chunk_number,
                "chunk_size": chunk_size,
                "total_urls_analyzed": len(urls),
                "successful_detections": successful_detections,
                "failed_detections": failed_detections,
                "unique_technologies": list(all_tech_slugs),
                "unique_tech_count": len(all_tech_slugs)
            }
        }
        
        logger.info(f"Stored {len(tech_detections)} tech detections in {chunk_number} chunks for operation {job_id}")

        # Update database with final results
        operations.update_operation(
            job_id=job_id,
            status=TaskStatus.COMPLETED,
            structured_output=structured_output,
            raw_output=[],  # Clear raw output on success
            end_time=datetime.now(),
            progress_perc=100
        )

        return {
            "job_id": job_id,
            "success": True,
            "message": f"Technology detection completed for {len(urls)} URLs. Successful: {successful_detections}, Failed: {failed_detections}",
            "count": len(tech_detections)
        }

    except Exception as e:
        logger.exception(f"Error in technology detection: {e}")

        # In case of failure, update database with error
        operations.update_operation(
            job_id=job_id,
            status=TaskStatus.FAILED,
            error=str(e),
            end_time=datetime.now()
        )

        return {"job_id": job_id, "success": False, "message": str(e)}


def _get_urls_from_db(domain: str) -> List[str]:
    """
    Get URLs from webserver discovery results stored in webserver_discovery collection
    
    Args:
        domain: Domain to get URLs for
        
    Returns:
        List[str]: List of URLs or empty list if none found
    """
    try:
        # Get webserver URLs from the webserver discovery collection for this domain
        urls = operations.get_webserver_urls_by_domain(domain)
        return urls
        
    except Exception as e:
        logger.error(f"Error retrieving webserver URLs for domain {domain}: {e}")
        return []


def _run_wappalyzer(url: str) -> Dict[str, Any]:
    """
    Run Wappalyzer technology detection on a URL
    
    Args:
        url: URL to analyze
        
    Returns:
        Dict[str, Any]: Wappalyzer output or empty dict if failed
    """
    try:
        # Run Wappalyzer CLI
        cmd = [
            'node', 
            '/opt/wappalyzer/src/drivers/npm/cli.js',
            url,
            '-p', 'full',
            '-a', '"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36"',
            '-D', '5',
            '-m', '30',
            '-w', '10000',
            '-r'
        ]
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=120
        )
        
        
        if result.returncode == 0 and result.stdout:
            return json.loads(result.stdout)
        else:
            logger.warning(f"Wappalyzer failed for {url}: {result.stderr}")
            logger.warning(f"Wappalyzer command: {' '.join(cmd)}")
            logger.warning(f"Return code: {result.returncode}")
            logger.warning(f"STDOUT: {result.stdout}")
            logger.warning(f"STDERR: {result.stderr}")

            return {}
            
    except subprocess.TimeoutExpired:
        logger.warning(f"Wappalyzer timeout for {url}")
        return {}
    except Exception as e:
        logger.warning(f"Wappalyzer error for {url}: {e}")
        return {} 