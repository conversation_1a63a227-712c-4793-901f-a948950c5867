"""
Subdomain enumeration task.
"""
import logging
import json
import uuid
from datetime import datetime
from collections import defaultdict

from app.db import operations
from app.models.task import TaskStatus
from app.tasks.celery_app import celery_app
from app.tasks.executor import execute_command

logger = logging.getLogger(__name__)

@celery_app.task(bind=True)
def run_subdomain_enumeration(self, domain: str, job_id: str = None):
    """Run subdomain enumeration for a domain"""
    try:
        # Create operation if needed
        if not job_id:
            job_id = operations.create_operation(
                feature="subdomain_enumeration",
                parameters={"domain": domain}
            )

        # Mark as started in database and store Celery task ID
        operations.update_operation(
            job_id=job_id,
            status=TaskStatus.RUNNING,
            start_time=datetime.now(),
            task_id=self.request.id  # Store Celery's task ID
        )

        # Update task state in Celery (not in database)
        self.update_state(state='STARTED', meta={'progress': 0, 'message': 'Starting subdomain enumeration'})

        # Execute command
        subfinder_cmd = [
            "/opt/subfinder",
            "-d", domain,
            "-silent",
            "-json",
            "-nW",
            "-rL", "/opt/wordlists/resolvers-trusted.txt",
            "-all",
            "-duc",
            "-provider-config", "/opt/configs/subfinder-provider.yaml",
            "-exclude-ip", "127.0.0.1",
            "-max-time", "20",
        ]
        success, message = execute_command(job_id, subfinder_cmd)
        
        # Update progress in Celery (not in database)
        self.update_state(state='STARTED', meta={'progress': 50, 'message': 'Processing results'})

        # Process results if successful
        if success:
            # Get the operation to access raw output
            operation = operations.get_operation(job_id)
            if not operation:
                logger.error(f"No operation found for task {job_id}")
                return {"job_id": job_id, "success": False, "message": "No operation found"}

            # Parse JSON output from subfinder
            subdomains = set()
            sources_dict = defaultdict(set)

            # Get raw output from operation
            raw_output = operation.get("raw_output", [])

            for line in raw_output:
                try:
                    data = json.loads(line)
                    host, source = data.get("host"), data.get("source")

                    if host:
                        subdomains.add(host)
                        if source:
                            sources_dict[source].add(host)
                except json.JSONDecodeError as e:
                    error_msg = f"Failed to parse JSON: {line}, error: {e}"
                    logger.warning(error_msg)

            # Update progress in Celery (not in database)
            self.update_state(state='STARTED', meta={'progress': 90, 'message': 'Finalizing results'})

            # Prepare structured output
            sorted_subdomains = sorted(subdomains)
            source_counts = {source: len(domains) for source, domains in sources_dict.items()}

            structured_output = {
                "subdomains": sorted_subdomains,
                "sources": source_counts,
                "count": len(sorted_subdomains),
            }

            # Update database with final results
            operations.update_operation(
                job_id=job_id,
                status=TaskStatus.COMPLETED,
                structured_output=structured_output,
                raw_output=[],  # Clear raw output on success
                end_time=datetime.now(),
                progress_perc=100
            )

            return {
                "job_id": job_id,
                "success": True,
                "message": f"Found {len(sorted_subdomains)} subdomains",
                "count": len(sorted_subdomains)
            }
        else:
            # If command execution failed, update database with error
            operations.update_operation(
                job_id=job_id,
                status=TaskStatus.FAILED,
                error=message,
                end_time=datetime.now()
            )

            return {"job_id": job_id, "success": False, "message": message}

    except Exception as e:
        logger.exception(f"Error in subdomain enumeration for domain {domain}: {e}")

        # In case of failure, update database with error
        operations.update_operation(
            job_id=job_id,
            status=TaskStatus.FAILED,
            error=str(e),
            end_time=datetime.now()
        )

        return {"job_id": job_id, "success": False, "message": str(e)}
