"""
DarkWeb leaks search tasks using DeHashed API.
"""
import logging
import os
import requests
import time
import random
from datetime import datetime
from typing import Dict, Any, Optional, Set, List

from app.db import operations
from app.models.task import TaskStatus
from app.tasks.celery_app import celery_app
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)

# DeHashed API configuration
DEHASHED_API_URL = os.getenv("DEHASHED_API_URL", "https://api.dehashed.com/v2")
DEHASHED_API_KEY = os.getenv("DEHASHED_API_KEY", "")
DEHASHED_MIN_DELAY = int(os.getenv("DEHASHED_MIN_DELAY", 1))
DEHASHED_MAX_DELAY = int(os.getenv("DEHASHED_MAX_DELAY", 3))

def query_dehashed_api(domain: str) -> Optional[Dict[str, Any]]:
    """
    Query DeHashed API with rate limiting.
    DeHashed returns maximum 10,000 results total, so single API call is sufficient.

    Args:
        domain: Domain to search for

    Returns:
        API response as JSON or None if the request fails
    """
    try:
        # Add random delay to avoid flooding the API
        delay = random.uniform(DEHASHED_MIN_DELAY, DEHASHED_MAX_DELAY)
        logger.info(f"Waiting {delay:.2f} seconds before querying internal API")
        time.sleep(delay)

        # Construct URL
        url = f"{DEHASHED_API_URL}/search"

        # Set headers
        headers = {
            'Content-Type': 'application/json',
            'DeHashed-Api-Key': DEHASHED_API_KEY
        }

        # Prepare JSON payload - get all available results (max 10,000)
        payload = {
            "query": f"domain:{domain}",
            "page": 1,
            "size": 10000,
            "de_dupe": True
        }

        # Make request
        logger.info(f"Querying internal API: {url} with payload {payload}")
        response = requests.post(url, json=payload, headers=headers, timeout=120)

        # Check if request was successful
        if response.status_code == 200:
            return response.json()
        else:
            error_msg = f"Internal API request failed with status code {response.status_code}: {response.text}"
            logger.error(error_msg)
            # Raise an exception to ensure the task fails
            raise Exception(error_msg)
    except Exception as e:
        logger.exception(f"Error querying internal API: {e}")
        # Re-raise the exception to ensure the task fails
        raise


@celery_app.task(bind=True)
def run_darkweb_search_by_domain(self, domain: str, job_id: str = None, chunk_size: int = 100):
    """
    Search for darkweb leaks by domain.
    DeHashed API returns maximum 10,000 results, so no pagination needed.

    Args:
        domain: Domain to search for
        job_id: Task ID (optional)
        chunk_size: Number of records per chunk for storage
    """
    try:
        # Create operation if needed
        if not job_id:
            job_id = operations.create_operation(
                feature="darkweb_leaks",
                parameters={"domain": domain}
            )

        # Mark as started in database and store Celery task ID
        operations.update_operation(
            job_id=job_id,
            status=TaskStatus.RUNNING,
            start_time=datetime.now(),
            task_id=self.request.id  # Store Celery's task ID
        )

        # Update task state in Celery (not in database)
        self.update_state(state='STARTED', meta={'progress': 10, 'message': 'Starting darkweb leaks search by domain'})

        # Initialize variables
        chunk_number = 0
        current_chunk = []
        unique_emails: Set[str] = set()
        databases: Set[str] = set()
        plain_text_passwords_count = 0
        hashed_passwords_count = 0

        try:
            # Update progress
            self.update_state(state='STARTED', meta={'progress': 30, 'message': 'Querying Data'})

            # Query DeHashed API (single call gets all available results)
            api_response = query_dehashed_api(domain)

            if not api_response:
                # Update database with error
                operations.update_operation(
                    job_id=job_id,
                    status=TaskStatus.FAILED,
                    error="Failed to get response from internal API",
                    end_time=datetime.now()
                )
                return {
                    "job_id": job_id,
                    "success": False,
                    "message": "Failed to get response from internal API"
                }

            # Extract data from response
            entries = api_response.get("entries", [])
            total_records = len(entries)
            total_from_api = api_response.get("total", 0)

            # Update progress
            self.update_state(state='STARTED', meta={'progress': 60, 'message': f'Processing {total_records} records'})

            logger.info(f"Found {total_records} records for domain {domain}")

            # Process records and add to chunks
            for entry in entries:
                # Extract metadata for summary
                if entry.get("email"):
                    if isinstance(entry["email"], list):
                        unique_emails.update(entry["email"])
                    else:
                        unique_emails.add(entry["email"])

                if entry.get("database_name"):
                    databases.add(entry["database_name"])

                # Count password types
                if entry.get("password"):
                    plain_text_passwords_count += len(entry["password"]) if isinstance(entry["password"], list) else 1
                if entry.get("hashed_password"):
                    hashed_passwords_count += len(entry["hashed_password"]) if isinstance(entry["hashed_password"], list) else 1

                # Add to current chunk (store raw DeHashed entry as-is)
                current_chunk.append(entry)

                # If chunk is full, store it
                if len(current_chunk) >= chunk_size:
                    operations.store_darkweb_leaks_chunk(job_id, domain, chunk_number, current_chunk)
                    chunk_number += 1
                    current_chunk = []

            # Update progress
            self.update_state(state='STARTED', meta={'progress': 90, 'message': 'Finalizing results'})

        except Exception as e:
            # Handle API errors
            error_msg = f"{str(e)}"
            logger.exception(error_msg)

            # Update database with error
            operations.update_operation(
                job_id=job_id,
                status=TaskStatus.FAILED,
                error=error_msg,
                end_time=datetime.now()
            )

            return {
                "job_id": job_id,
                "success": False,
                "message": error_msg
            }

        # Store any remaining records in the last chunk
        if current_chunk:
            operations.store_darkweb_leaks_chunk(job_id, domain, chunk_number, current_chunk)
            chunk_number += 1

        # Prepare summary for the main operation document
        summary = {
            "total_records": total_records,
            "total_chunks": chunk_number,
            "chunk_size": chunk_size,
            "unique_emails_count": len(unique_emails),
            "plain_text_passwords_count": plain_text_passwords_count,
            "hashed_passwords_count": hashed_passwords_count,
            "databases_count": len(databases)
        }

        # Prepare structured output with statistics (not full data)
        structured_output = {
            "search_parameters": {
                "domain": domain
            },
            "statistics": summary
        }

        # Update database with final results
        operations.update_operation(
            job_id=job_id,
            status=TaskStatus.COMPLETED,
            structured_output=structured_output,
            end_time=datetime.now(),
            progress_perc=100
        )

        return {
            "job_id": job_id,
            "success": True,
            "message": f"DarkWeb leaks search completed. Found {total_records} records across {chunk_number} chunks."
        }
    except Exception as e:
        logger.exception(f"Error in darkweb leaks search by domain {domain}: {e}")

        # In case of failure, update database with error
        operations.update_operation(
            job_id=job_id,
            status=TaskStatus.FAILED,
            error=str(e),
            end_time=datetime.now()
        )

        return {"job_id": job_id, "success": False, "message": str(e)} 