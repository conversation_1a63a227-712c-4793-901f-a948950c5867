"""
Git search tasks using git-hound tool.
"""
import logging
import os
import subprocess
import json
import tempfile
from datetime import datetime
from typing import Dict, Any, Optional, List
from pathlib import Path

from app.db import operations
from app.models.task import TaskStatus
from app.tasks.celery_app import celery_app
from app.utils.data_loader import data_loader
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)

# Git-hound configuration
GIT_TOKENS = os.getenv("GIT_TOKENS", "").split(",") if os.getenv("GIT_TOKENS") else []


def run_git_hound_command(identifiers: List[str], git_depth: int) -> List[Dict[str, Any]]:
    """
    Run git-hound command and parse JSONL output.

    Args:
        identifiers: List of identifiers to search for
        git_depth: Number of pages to search

    Returns:
        List of parsed JSON objects from git-hound output
    """
    try:
        if not G<PERSON>_TOKENS or not GIT_TOKENS[0].strip():
            raise Exception("GIT_TOKENS environment variable not set")

        # Get githound-rules directory path
        githound_rules_path = data_loader.get_git_search_rules_path()
        if not githound_rules_path.exists():
            raise Exception(f"Githound rules directory not found: {githound_rules_path}")

        # Prepare git-hound command
        tokens_str = ",".join(token.strip() for token in GIT_TOKENS if token.strip())
        
        cmd = [
            "/opt/git-hound",
            "--tokens", tokens_str,
            "--threads", "100",
            "--rules", str(githound_rules_path),
            "--pages", str(git_depth),
            "--json"
        ]

        # Add query parameters for each identifier
        for identifier in identifiers:
            cmd.extend(["--query", identifier])

        logger.info(f"Running git-hound command: {' '.join(cmd)}")

        # Run git-hound command
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=3600  # 1 hour timeout
        )

        if result.returncode != 0:
            error_msg = f"Git-hound command failed with return code {result.returncode}: {result.stderr}"
            logger.error(error_msg)
            raise Exception(error_msg)

        # Parse JSONL output - filter lines that start with '{"attributes'
        parsed_results = []
        for line in result.stdout.strip().split('\n'):
            line = line.strip()
            if line and line.startswith('{"attributes'):
                try:
                    json_obj = json.loads(line)
                    parsed_results.append(json_obj)
                except json.JSONDecodeError as e:
                    logger.warning(f"Failed to parse JSON line: {line}, error: {e}")
                    continue

        logger.info(f"Git-hound found {len(parsed_results)} results")
        return parsed_results

    except subprocess.TimeoutExpired:
        error_msg = "Git-hound command timed out after 1 hour"
        logger.error(error_msg)
        raise Exception(error_msg)
    except Exception as e:
        logger.exception(f"Error running git-hound command: {e}")
        raise


def process_git_hound_result(result: Dict[str, Any]) -> Dict[str, Any]:
    """
    Process a single git-hound result and normalize the data structure.

    Args:
        result: Raw git-hound result

    Returns:
        Processed result with normalized fields
    """
    processed = {
        "attributes": result.get("attributes", []),
        "context": result.get("context", ""),
        "match": result.get("match", ""),
        "repo": result.get("repo", ""),
        "url": result.get("url", ""),
        "file_last_author": result.get("file_last_author", ""),
        "file_last_updated": result.get("file_last_updated", ""),
        "processed_at": datetime.now().isoformat()
    }
    
    # Extract additional metadata
    if processed["attributes"]:
        processed["rule_id"] = processed["attributes"][0] if len(processed["attributes"]) > 0 else ""
        processed["rule_description"] = processed["attributes"][1] if len(processed["attributes"]) > 1 else ""
    
    # Extract repository name from URL
    if processed["repo"]:
        try:
            repo_parts = processed["repo"].rstrip("/").split("/")
            if len(repo_parts) >= 2:
                processed["repo_owner"] = repo_parts[-2]
                processed["repo_name"] = repo_parts[-1]
        except Exception:
            pass

    return processed


@celery_app.task(bind=True)
def run_git_search_by_identifiers(self, identifiers: List[str], job_id: str = None, chunk_size: int = 100, git_depth: int = 10):
    """
    Search for GitHub leaks containing the specified identifiers using git-hound.

    Args:
        identifiers: List of identifiers to search for
        job_id: Task ID (optional)
        chunk_size: Number of records per chunk for storage
        git_depth: Number of git-hound pages to search
    """
    try:
        # Create operation if needed
        if not job_id:
            job_id = operations.create_operation(
                feature="git_search",
                parameters={"identifiers": identifiers, "git_depth": git_depth}
            )

        # Mark as started in database and store Celery task ID
        operations.update_operation(
            job_id=job_id,
            status=TaskStatus.RUNNING,
            start_time=datetime.now(),
            task_id=self.request.id  # Store Celery's task ID
        )

        # Update task state in Celery
        self.update_state(state='STARTED', meta={'progress': 0, 'message': 'Starting git search by identifiers'})

        # Initialize variables
        chunk_number = 0
        current_chunk = []
        total_results = 0
        repos_found = set()
        date_range = {"min": None, "max": None}
        rule_types = set()

        try:
            # Run git-hound command
            self.update_state(state='PROGRESS', meta={'progress': 10, 'message': 'Running git search'})
            raw_results = run_git_hound_command(identifiers, git_depth)

            if not raw_results:
                logger.info("No results found by git-hound")
            else:
                logger.info(f"Processing {len(raw_results)} git-hound results")

            # Process each result
            for i, raw_result in enumerate(raw_results):
                try:
                    # Process the result
                    processed_result = process_git_hound_result(raw_result)
                    current_chunk.append(processed_result)
                    total_results += 1

                    # Track metrics
                    if processed_result.get("repo"):
                        repos_found.add(processed_result["repo"])
                    
                    if processed_result.get("rule_description"):
                        rule_types.add(processed_result["rule_description"])

                    # Track date range
                    if processed_result.get("file_last_updated"):
                        updated_date = processed_result["file_last_updated"]
                        if date_range["min"] is None or updated_date < date_range["min"]:
                            date_range["min"] = updated_date
                        if date_range["max"] is None or updated_date > date_range["max"]:
                            date_range["max"] = updated_date

                    # Save chunk when it reaches the specified size
                    if len(current_chunk) >= chunk_size:
                        operations.save_git_search_chunk(job_id, chunk_number, current_chunk)
                        chunk_number += 1
                        current_chunk = []

                    # Update progress
                    if i % 10 == 0:  # Update every 10 results
                        progress = 10 + int(80 * (i + 1) / len(raw_results))
                        self.update_state(state='PROGRESS', meta={
                            'progress': progress, 
                            'message': f'Processing result {i + 1}/{len(raw_results)}'
                        })

                except Exception as e:
                    logger.warning(f"Error processing result {i}: {e}")
                    continue

            # Save any remaining results in the last chunk
            if current_chunk:
                operations.save_git_search_chunk(job_id, chunk_number, current_chunk)
                chunk_number += 1

            # Calculate total chunks
            total_chunks = chunk_number

            # Prepare summary
            summary = {
                "total_records": total_results,
                "total_chunks": total_chunks,
                "chunk_size": chunk_size,
                "repositories_found": len(repos_found),
                "unique_repos": list(repos_found),  # Store first 20 repo names
                "rule_types": list(rule_types),
                "date_range": date_range,
                "identifiers_searched": identifiers,
                "git_depth": git_depth
            }

            # Update operation with completion status
            self.update_state(state='PROGRESS', meta={'progress': 95, 'message': 'Finalizing results'})
            operations.update_operation(
                job_id=job_id,
                status=TaskStatus.COMPLETED,
                end_time=datetime.now(),
                structured_output={"statistics": summary}
            )

            # Final Celery state update
            self.update_state(
                state='SUCCESS',
                meta={
                    'progress': 100,
                    'message': f'Git search completed. Found {total_results} results across {len(repos_found)} repositories.',
                    'summary': summary
                }
            )

            return summary

        except Exception as e:
            error_msg = f"Error during git search execution: {str(e)}"
            logger.exception(error_msg)
            
            # Update operation with failure status
            operations.update_operation(
                job_id=job_id,
                status=TaskStatus.FAILED,
                end_time=datetime.now(),
                error=error_msg
            )
            
            # Update Celery task state
            self.update_state(
                state='FAILURE',
                meta={
                    'progress': 0,
                    'message': error_msg,
                    'error': error_msg
                }
            )
            
            raise Exception(error_msg)

    except Exception as e:
        error_msg = f"Error in git search task: {str(e)}"
        logger.exception(error_msg)
        
        # Ensure operation is marked as failed
        if job_id:
            operations.update_operation(
                job_id=job_id,
                status=TaskStatus.FAILED,
                end_time=datetime.now(),
                error=error_msg
            )
        
        raise Exception(error_msg) 