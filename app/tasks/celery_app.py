import os
import logging
from celery import Celery
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)

# Redis connection
REDIS_URI = os.getenv("REDIS_URI", "redis://localhost:6379/0")

# Create Celery app
celery_app = Celery(
    "threatmesh",
    broker=REDIS_URI,
    backend=REDIS_URI,
)

# Configure Celery
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    broker_connection_retry_on_startup=True,
)

# Import tasks to register them (but avoid circular imports)
from app.tasks.asm import subdomain_enumeration, public_ip_search, webserver_discovery, tech_detection, stack_vulnerabilities, nuclei_scan
from app.tasks.cti import phishing_domains, pastes_search, git_search, darkweb_leaks

# Note: Tasks will be auto-discovered by <PERSON><PERSON><PERSON>, no need for explicit imports
