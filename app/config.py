"""
Global configuration settings for the ThreatMesh application.
"""
from datetime import timedelta

# Task expiration settings
# These define how long a completed task's results are considered "recent"
# and will be returned instead of starting a new task
TASK_EXPIRATION = {
    # Default expiration time if not specified
    "default": timedelta(hours=6),

    # Feature-specific expiration times
    "subdomain_enumeration": timedelta(hours=24),
    "public_ip_search": timedelta(hours=12),
    "webserver_discovery": timedelta(hours=12),
    "tech_detection": timedelta(days=30),  # Tech detection results are more stable
    "stack_vulnerabilities": timedelta(days=7),  # Stack vulnerabilities change less frequently
    "nuclei_scan": timedelta(hours=6),  # Nuclei vulnerability scans

    "darkweb_leaks": timedelta(hours=6),
    "git_search": timedelta(hours=6),
    "pastes_search": timedelta(hours=6),
    "phishing_domains": timedelta(hours=6),
}

# Convert time periods for convenience functions
def minutes(n: int) -> timedelta:
    """Convert minutes to timedelta"""
    return timedelta(minutes=n)

def hours(n: int) -> timedelta:
    """Convert hours to timedelta"""
    return timedelta(hours=n)

def days(n: int) -> timedelta:
    """Convert days to timedelta"""
    return timedelta(days=n)

def months(n: int) -> timedelta:
    """Convert months (approximate) to timedelta"""
    return timedelta(days=n * 30)  # Approximate
