"""
Data Loader Utility

Simplified utility for loading data files from the app/data directory.
"""

import json
import yaml
from pathlib import Path
from typing import Dict, List, Any


class DataLoader:
    """Simple data file loader."""
    
    def __init__(self):
        """Initialize data loader paths."""
        self.app_dir = Path(__file__).parent.parent
        self.data_dir = self.app_dir / 'data'
        self.rules_dir = self.app_dir.parent / 'rules'
        self.secret_patterns_file = self.data_dir / 'cti' / 'SecretDetector' / 'secret_patterns.yaml'
        self.githound_rules_dir = self.data_dir / 'cti' / 'git_search' / 'githound-rules' 
        
        # Create directories if needed
        self.data_dir.mkdir(exist_ok=True)
        (self.data_dir / 'cti' / 'SecretDetector').mkdir(parents=True, exist_ok=True)
        (self.data_dir / 'cti' / 'git_search' / 'githound-rules').mkdir(parents=True, exist_ok=True)
    
    def load_yaml(self, file_path: str, from_rules: bool = False) -> Dict[str, Any]:
        """Load YAML file."""
        base_dir = self.rules_dir if from_rules else self.data_dir
        full_path = base_dir / file_path
        
        with open(full_path, 'r', encoding='utf-8') as file:
            return yaml.safe_load(file) or {}
    
    def load_json(self, file_path: str, from_rules: bool = False) -> Dict[str, Any]:
        """Load JSON file."""
        base_dir = self.rules_dir if from_rules else self.data_dir
        full_path = base_dir / file_path
        
        with open(full_path, 'r', encoding='utf-8') as file:
            return json.load(file)
    
    def secret_patterns_exists(self) -> bool:
        """Check if secret patterns file exists."""
        return self.secret_patterns_file.exists()
    
    def get_secret_patterns_path(self) -> Path:
        """Get secret patterns file path."""
        return self.secret_patterns_file
    
    def get_git_search_rules_path(self) -> Path:
        """Get git search rules directory path."""
        return self.githound_rules_dir


# Global instance
data_loader = DataLoader()


# Simple convenience functions
def load_yaml_data(file_path: str, from_rules: bool = False) -> Dict[str, Any]:
    """Load YAML data file."""
    return data_loader.load_yaml(file_path, from_rules)

def load_json_data(file_path: str, from_rules: bool = False) -> Dict[str, Any]:
    """Load JSON data file."""
    return data_loader.load_json(file_path, from_rules)

def load_secret_patterns() -> Dict[str, Any]:
    """Load secret detection patterns."""
    with open(data_loader.secret_patterns_file, 'r', encoding='utf-8') as file:
        return yaml.safe_load(file) or {} 