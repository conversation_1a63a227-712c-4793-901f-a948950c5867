"""
Utility functions for creating API responses.
"""
import logging
from typing import Optional, Dict, Any

from app.models.task import TaskStatus, TaskResponse

logger = logging.getLogger(__name__)

def create_task_response(operation: Dict[str, Any]) -> TaskResponse:
    """
    Helper function to create a task response from an operation document.

    Args:
        operation: Operation document from MongoDB

    Returns:
        TaskResponse object with all relevant information
    """
    # Extract job_id from the operation document
    job_id = operation["_id"]

    response = TaskResponse(
        job_id=job_id,
        status=operation.get("status"),
        feature=operation.get("feature", "not_described"),
        start_time=operation.get("start_time"),
        end_time=operation.get("end_time"),
    )

    # Calculate elapsed seconds if both start and end times are available
    if operation.get("start_time") and operation.get("end_time"):
        response.elapsed_seconds = (operation["end_time"] - operation["start_time"]).total_seconds()

    # Add results if available
    if operation.get("structured_output"):
        response.results = operation.get("structured_output")



    # Add error information if operation failed
    if operation.get("status") in [TaskStatus.FAILED, TaskStatus.KILLED, TaskStatus.SUSPENDED]:
        error_msg = operation.get("error") or "Unknown error occurred"

        # Include raw output in the error message if available
        if operation.get("raw_output"):
            raw_output = "\n".join(operation.get("raw_output"))
            error_msg = f"{error_msg}\n\nRaw output:\n{raw_output}"

        response.error = error_msg

    # Add progress percentage if available
    if operation.get("progress_perc") is not None:
        response.progress_perc = operation.get("progress_perc")

    # Add message if available
    if operation.get("message"):
        response.message = operation.get("message")

    return response
