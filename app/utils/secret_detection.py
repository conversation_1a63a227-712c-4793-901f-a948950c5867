"""
Secret Detection Utility

This module provides functionality to detect secrets and sensitive information
in text content using configurable regex patterns from a YAML file.
"""

import re
import yaml
from typing import List, Dict, Any
from pathlib import Path
from .data_loader import data_loader, load_secret_patterns


class SecretMatch:
    """Represents a detected secret match."""
    
    def __init__(self, pattern_name: str, matched_content: str, confidence: str, start_pos: int, end_pos: int):
        self.pattern_name = pattern_name
        self.matched_content = matched_content
        self.confidence = confidence
        self.start_pos = start_pos
        self.end_pos = end_pos
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert SecretMatch to dictionary representation."""
        return {
            'pattern_name': self.pattern_name,
            'matched_content': self.matched_content,
            'confidence': self.confidence
        }
    
    def __str__(self) -> str:
        return f"SecretMatch(pattern='{self.pattern_name}', content='{self.matched_content[:50]}...', confidence='{self.confidence}')"


class SecretDetector:
    """Secret detection engine that loads patterns and performs detection."""
    
    def __init__(self, patterns_path: str = None):
        """Initialize the secret detector with patterns from YAML file."""
        if patterns_path is None:
            # Default to patterns file in the same directory
            current_dir = Path(__file__).parent
            patterns_path = current_dir / "secret_patterns.yaml"
        
        self.patterns_path = patterns_path
        self.patterns = self._load_patterns()
    
    def _load_patterns(self) -> List[Dict[str, Any]]:
        """Load secret detection patterns from YAML file."""
        try:
            # Try to load from new data directory structure first
            if data_loader.secret_patterns_exists():
                data = load_secret_patterns()
        
            else:
                raise FileNotFoundError(f"Patterns file not found. Expected at: {data_loader.get_secret_patterns_path()} or fallback: {self.patterns_path}")
            
            return data.get('patterns', [])
        except FileNotFoundError as e:
            raise e
        except ValueError as e:  # This covers YAML errors from load_secret_patterns()
            raise e
        except Exception as e:
            raise RuntimeError(f"Error loading patterns file: {e}")
    
    def detect_secrets(self, content: str) -> List[SecretMatch]:
        """
        Detect secrets in the provided content using loaded patterns.
        
        Args:
            content (str): The text content to scan for secrets
            
        Returns:
            List[SecretMatch]: List of detected secret matches
        """
        if not isinstance(content, str):
            raise TypeError("Content must be a string")
        
        matches = []
        
        for pattern_config in self.patterns:
            pattern_info = pattern_config.get('pattern', {})
            pattern_name = pattern_info.get('name', 'Unknown Pattern')
            regex_pattern = pattern_info.get('regex', '')
            confidence = pattern_info.get('confidence', 'unknown')
            
            if not regex_pattern:
                continue
            
            try:
                # Compile regex pattern
                compiled_pattern = re.compile(regex_pattern, re.MULTILINE | re.DOTALL)
                
                # Find all matches
                for match in compiled_pattern.finditer(content):
                    secret_match = SecretMatch(
                        pattern_name=pattern_name,
                        matched_content=match.group(0),
                        confidence=confidence,
                        start_pos=match.start(),
                        end_pos=match.end()
                    )
                    matches.append(secret_match)
                    
            except re.error as e:
                # Log regex compilation error but continue with other patterns
                print(f"Warning: Invalid regex pattern for '{pattern_name}': {e}")
                continue
        
        return matches


# Initialize detector instance once at module import time
# This loads patterns fresh when app starts and keeps them in memory
_detector_instance = SecretDetector()


def SecretDetection(content: str) -> List[Dict[str, Any]]:
    """
    Main function to detect secrets in content.
    
    This function provides a simple interface to the secret detection functionality.
    It returns a list of dictionaries containing information about detected secrets.
    Patterns are loaded once when the app starts and cached for the entire app lifetime.
    
    Args:
        content (str): The text content to scan for secrets
        
    Returns:
        List[Dict[str, Any]]: List of detected secrets with the following structure:
            - pattern_name: Name of the matched pattern
            - matched_content: The actual content that matched
            - confidence: Confidence level (high, medium, low)
            
    Example:
        >>> from app.utils.secret_detection import SecretDetection
        >>> content = "My API key is AKIAIOSFODNN7EXAMPLE"
        >>> results = SecretDetection(content)
        >>> print(results)
        [{'pattern_name': 'AWS API Key', 'matched_content': 'AKIAIOSFODNN7EXAMPLE', 
          'confidence': 'high'}]
    """
    # Use the pre-loaded detector instance for optimal performance
    matches = _detector_instance.detect_secrets(content)
    return [match.to_dict() for match in matches] 