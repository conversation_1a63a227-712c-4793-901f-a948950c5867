from typing import Optional, <PERSON><PERSON>
import re
import validators
from fastapi import HTTPException, status, Query
from pydantic import BaseModel, field_validator

class DomainValidator(BaseModel):
    """Validator for domain names"""
    domain: str

    @field_validator('domain')
    def validate_domain(cls, v):
        """Validate domain format"""
        if not v:
            raise ValueError("Domain cannot be empty")

        if not validators.domain(v):
            raise ValueError("Invalid domain format. Example of valid domain: example.com")

        return v

def validate_domain_param(
    domain: Optional[str] = Query(None, description="Target domain (e.g., example.com)"),
    job_id: Optional[str] = Query(None, description="Task ID from a previous request"),
    ignore_cache: bool = Query(False, description="Force a new scan, bypassing stored results")
):
    """
    Validate domain parameter or ensure job_id is provided

    Args:
        domain: Domain name to validate
        job_id: Task ID from a previous request
        ignore_cache: Force a new scan, bypassing stored results

    Returns:
        Tuple of (domain, job_id, ignore_cache)

    Raises:
        HTTPException: If validation fails
    """
    # If job_id is provided, we don't need to validate domain
    if job_id:
        return domain, job_id, ignore_cache

    # If neither domain nor job_id is provided, raise an error
    if not domain:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Either 'domain' or 'job_id' parameter must be provided"
        )

    # Validate domain format
    try:
        DomainValidator(domain=domain)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

    return domain, job_id, ignore_cache

def validate_webservers_query_params(
    domain: Optional[str] = Query(None, description="Target domain (e.g., example.com)"),
    job_id: Optional[str] = Query(None, description="Task ID from a previous request"),
    ignore_cache: bool = Query(False, description="Force a new scan, bypassing stored results")
):
    """
    Validate webserver query parameters for both GET and POST requests

    Args:
        domain: Domain name to use existing subdomain results (GET only)
        job_id: Task ID from a previous request
        ignore_cache: Force a new scan, bypassing stored results

    Returns:
        Tuple of (domain, job_id, ignore_cache)

    Raises:
        HTTPException: If validation fails
    """
    # If job_id is provided, we don't need to validate domain
    if job_id:
        return domain, job_id, ignore_cache

    # For GET requests, domain is required if no job_id
    # For POST requests, domain is optional (subdomains come from body)
    if domain:
        # Validate domain format if provided
        try:
            DomainValidator(domain=domain)
        except ValueError as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=str(e)
            )

    return domain, job_id, ignore_cache

def validate_tech_detection_query_params(
    domain: Optional[str] = Query(None, description="Target domain (e.g., example.com)"),
    job_id: Optional[str] = Query(None, description="Task ID from a previous request"),
    ignore_cache: bool = Query(False, description="Force a new scan, bypassing stored results")
):
    """
    Validate technology detection query parameters for both GET and POST requests

    Args:
        domain: Domain name to use existing webserver results (GET only)
        job_id: Task ID from a previous request
        ignore_cache: Force a new scan, bypassing stored results

    Returns:
        Tuple of (domain, job_id, ignore_cache)

    Raises:
        HTTPException: If validation fails
    """
    # If job_id is provided, we don't need to validate domain
    if job_id:
        return domain, job_id, ignore_cache

    # For GET requests, domain is required if no job_id
    # For POST requests, domain is optional (URLs come from body)
    if domain:
        # Validate domain format if provided
        try:
            DomainValidator(domain=domain)
        except ValueError as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=str(e)
            )

    return domain, job_id, ignore_cache

def validate_nuclei_scan_query_params(
    job_id: Optional[str] = Query(None, description="Task ID from a previous request"),
    ignore_cache: bool = Query(False, description="Force a new scan, bypassing stored results")
):
    """
    Validate nuclei scan query parameters for POST requests

    Args:
        job_id: Task ID from a previous request
        ignore_cache: Force a new scan, bypassing stored results

    Returns:
        Tuple of (job_id, ignore_cache)

    Raises:
        HTTPException: If validation fails
    """
    # For nuclei scan, job_id is optional (only used for status checking)
    # CVE IDs and targets come from request body
    return job_id, ignore_cache