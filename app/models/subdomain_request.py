"""
Request models for subdomain enumeration API.
"""
from typing import Optional
from pydantic import BaseModel, validator
import validators


class SubdomainRequest(BaseModel):
    """Request model for subdomain enumeration"""
    domain: str
    quick: Optional[bool] = False

    @validator('domain')
    def validate_domain(cls, v):
        """Validate domain format"""
        if not validators.domain(v):
            raise ValueError(f"Invalid domain format: {v}")
        return v
