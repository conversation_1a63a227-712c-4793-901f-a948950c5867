"""
Request models for public IPs discovery API.
"""
from typing import Optional
from pydantic import BaseModel, validator
import validators


class PublicIpsRequest(BaseModel):
    """Request model for public IPs discovery"""
    domain: str
    depth: Optional[int] = 0
    
    @validator('domain')
    def validate_domain(cls, v):
        """Validate domain format"""
        if not validators.domain(v):
            raise ValueError(f"Invalid domain format: {v}")
        return v
    
    @validator('depth')
    def validate_depth(cls, v):
        """Validate depth parameter"""
        if v < 0:
            raise ValueError("Depth must be non-negative")
        return v
