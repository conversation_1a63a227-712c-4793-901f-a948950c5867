"""
Request models for nuclei scan API.
"""
from typing import List
from pydantic import BaseModel, validator
import re


class NucleiScanRequest(BaseModel):
    """Request model for nuclei scan POST endpoint"""
    cve_ids: List[str]
    targets: List[str]
    distributed: bool = False

    @validator('cve_ids')
    def validate_cve_ids(cls, v):
        """Validate CVE IDs list"""
        if not v:
            raise ValueError('CVE IDs list cannot be empty')
        
        if len(v) > 200:  # Reasonable limit for CVE IDs
            raise ValueError('Too many CVE IDs. Maximum 200 CVE IDs allowed per request')
        
        # Basic validation - just check they're non-empty strings
        for cve_id in v:
            if not cve_id or not isinstance(cve_id, str):
                raise ValueError(f'Invalid CVE ID: {cve_id}')
            
            # Check for empty or whitespace-only strings
            if not cve_id.strip():
                raise ValueError('CVE ID cannot be empty or whitespace only')
        
        # Return CVE IDs as provided (no normalization)
        return v

    @validator('targets')
    def validate_targets(cls, v):
        """Validate targets list"""
        if not v:
            raise ValueError('Targets list cannot be empty')
        
        if len(v) > 1000:  # Reasonable limit for targets
            raise ValueError('Too many targets. Maximum 1000 targets allowed per request')
        
        # Validate each target (can be domain, subdomain, or IP)
        for target in v:
            if not target or not isinstance(target, str):
                raise ValueError(f'Invalid target: {target}')
            
            # Basic validation - check for valid characters and length
            if len(target) > 253:  # Max domain length
                raise ValueError(f'Target too long: {target}')
            
            # Allow domains, subdomains, and IP addresses
            # Basic pattern check for valid characters
            if not re.match(r'^[a-zA-Z0-9.-]+$', target):
                raise ValueError(f'Invalid target format: {target}. Only alphanumeric characters, dots, and hyphens allowed')
        
        return v

    @validator('distributed')
    def validate_distributed(cls, v):
        """Validate distributed flag"""
        if not isinstance(v, bool):
            raise ValueError('Distributed flag must be a boolean')
        return v

    class Config:
        json_schema_extra = {
            "example": {
                "cve_ids": [
                    "CVE-2021-44228",
                    "CVE-2023-41221",
                    "CVE-2024-1234"
                ],
                "targets": [
                    "example.com",
                    "api.example.com",
                    "***********"
                ],
                "distributed": False
            }
        } 