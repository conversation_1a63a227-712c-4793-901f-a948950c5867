"""
Request models for git search API.
"""
from typing import List, Optional
from pydantic import BaseModel, validator
import json


class GitSearchRequest(BaseModel):
    """Request model for git search"""
    identifiers: List[str]
    git_depth: Optional[int] = 10
    
    @validator('identifiers')
    def validate_identifiers(cls, v):
        """Validate identifiers list"""
        if not v or len(v) == 0:
            raise ValueError("Identifiers list cannot be empty")
        return v
    
    @validator('git_depth')
    def validate_git_depth(cls, v):
        """Validate git depth"""
        if v < 1 or v > 50:
            raise ValueError("Git depth must be between 1 and 50")
        return v
