"""
Request models for technology detection API.
"""
from typing import List, Optional
from pydantic import BaseModel, validator
from urllib.parse import urlparse
import validators


class TechDetectionRequest(BaseModel):
    """Request model for domain-based technology detection"""
    domain: str

    @validator('domain')
    def validate_domain(cls, v):
        """Validate domain format"""
        if not validators.domain(v):
            raise ValueError(f"Invalid domain format: {v}")
        return v


class TechDetectionPostRequest(BaseModel):
    """Request model for technology detection POST endpoint"""
    urls: List[str]

    @validator('urls')
    def validate_urls(cls, v):
        """Validate URLs list"""
        if not v:
            raise ValueError('URLs list cannot be empty')
        
        if len(v) > 100:  # Reasonable limit for URLs
            raise ValueError('Too many URLs. Maximum 100 URLs allowed per request')
        
        # Validate each URL format
        for url in v:
            if not url or not isinstance(url, str):
                raise ValueError(f'Invalid URL: {url}')
            
            # Basic URL validation
            try:
                parsed = urlparse(url)
                if not parsed.scheme or not parsed.netloc:
                    raise ValueError(f'Invalid URL format: {url}')
                
                # Must be HTTP or HTTPS
                if parsed.scheme not in ['http', 'https']:
                    raise ValueError(f'URL must use http or https protocol: {url}')
                    
            except Exception as e:
                raise ValueError(f'Invalid URL format: {url} - {str(e)}')
        
        return v

    class Config:
        json_schema_extra = {
            "example": {
                "urls": [
                    "https://api.example.com",
                    "https://blog.example.com",
                    "http://admin.example.com"
                ]
            }
        } 