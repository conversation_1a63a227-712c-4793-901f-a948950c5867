"""
Request models for phishing domains API.
"""
from pydantic import BaseModel, validator
import validators


class PhishingDomainsRequest(BaseModel):
    """Request model for phishing domains detection"""
    domain: str
    
    @validator('domain')
    def validate_domain(cls, v):
        """Validate domain format"""
        if not validators.domain(v):
            raise ValueError(f"Invalid domain format: {v}")
        return v
