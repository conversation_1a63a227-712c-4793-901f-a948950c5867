"""
Pydantic models for webserver discovery requests.
"""
from typing import List
from pydantic import BaseModel, field_validator
import validators


class WebserverDiscoveryPostRequest(BaseModel):
    """Request model for POST webserver discovery API (subdomains in body)"""
    
    subdomains: List[str]
    
    @field_validator('subdomains')
    def validate_subdomains(cls, v):
        """Validate subdomains list"""
        if not isinstance(v, list):
            raise ValueError("Subdomains must be a list")
        
        if len(v) == 0:
            raise ValueError("Subdomains list cannot be empty")
            
        for subdomain in v:
            if not validators.domain(subdomain):
                raise ValueError(f"Invalid subdomain format: {subdomain}")
        return v 