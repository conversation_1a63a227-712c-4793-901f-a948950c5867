from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field

class TaskStatus(str, Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    KILLED = "killed"
    SUSPENDED = "suspended"

class Task(BaseModel):
    id: Optional[str] = None
    feature: str
    parameters: Dict[str, Any]
    status: TaskStatus = TaskStatus.PENDING
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None

    class Config:
        populate_by_name = True

    def to_mongo(self) -> Dict[str, Any]:
        """Convert to MongoDB document"""
        data = self.model_dump(by_alias=True)
        if self.id is None:
            data.pop("id", None)
        else:
            data["_id"] = data.pop("id")
        return data

    @classmethod
    def from_mongo(cls, data: Dict[str, Any]) -> "Task":
        """Create from MongoDB document"""
        if data.get("_id"):
            data["id"] = str(data.pop("_id"))
        return cls(**data)

class TaskResult(BaseModel):
    job_id: str
    raw_output: List[str] = []
    structured_output: Optional[Dict[str, Any]] = None

    class Config:
        populate_by_name = True

    def to_mongo(self) -> Dict[str, Any]:
        """Convert to MongoDB document"""
        return self.model_dump(by_alias=True)

    @classmethod
    def from_mongo(cls, data: Dict[str, Any]) -> "TaskResult":
        """Create from MongoDB document"""
        if data.get("_id"):
            data.pop("_id")
        return cls(**data)

class TaskResponse(BaseModel):
    job_id: str
    status: TaskStatus
    feature: str
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    elapsed_seconds: Optional[float] = None
    results: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    progress_perc: Optional[int] = None
    message: Optional[str] = None
    cache_hit: bool = False

    # This allows the model to accept additional fields not defined in the schema
    # Needed for the detailed records which might have varying structure
    model_config = {
        "extra": "allow"
    }


