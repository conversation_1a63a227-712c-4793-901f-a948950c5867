"""
Request models for stack vulnerabilities API.
"""
from typing import Optional
from pydantic import BaseModel, validator
import validators


class StackVulnerabilitiesRequest(BaseModel):
    """Request model for stack vulnerabilities analysis"""
    domain: Optional[str] = None
    cpe: Optional[str] = None
    
    @validator('domain')
    def validate_domain(cls, v):
        """Validate domain format"""
        if v and not validators.domain(v):
            raise ValueError(f"Invalid domain format: {v}")
        return v
    
    @validator('cpe')
    def validate_cpe(cls, v):
        """Validate CPE format"""
        if v and not v.startswith('cpe:'):
            raise ValueError("CPE must start with 'cpe:'")
        return v
    
    def __init__(self, **data):
        super().__init__(**data)
        # Ensure at least one of domain or cpe is provided
        if not self.domain and not self.cpe:
            raise ValueError("Either 'domain' or 'cpe' must be provided")
