"""
Request models for darkweb leaks API.
"""
from pydantic import BaseModel, validator
import validators


class DarkwebLeaksRequest(BaseModel):
    """Request model for darkweb leaks search"""
    domain: str
    
    @validator('domain')
    def validate_domain(cls, v):
        """Validate domain format"""
        if not validators.domain(v):
            raise ValueError(f"Invalid domain format: {v}")
        return v
