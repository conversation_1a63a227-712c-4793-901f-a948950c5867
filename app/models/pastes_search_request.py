"""
Request models for pastes search API.
"""
from typing import Optional
from pydantic import BaseModel, validator
import validators


class PastesSearchRequest(BaseModel):
    """Request model for pastes search"""
    domain: str
    secrets: Optional[bool] = False
    cse_page_limit: Optional[int] = 10
    
    @validator('domain')
    def validate_domain(cls, v):
        """Validate domain format"""
        if not validators.domain(v):
            raise ValueError(f"Invalid domain format: {v}")
        return v
    
    @validator('cse_page_limit')
    def validate_cse_page_limit(cls, v):
        """Validate CSE page limit"""
        if v < 1 or v > 100:
            raise ValueError("CSE page limit must be between 1 and 100")
        return v
