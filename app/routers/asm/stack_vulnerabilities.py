"""
ASM Stack Vulnerabilities Router
"""
from fastapi import APIRouter, HTTPException, status, Query, Depends
from typing import Optional, Dict, Any, Tuple
from datetime import datetime
from math import ceil
import logging

from app.db import operations
from app.models.task import TaskStatus, ExecutionResponse, TaskResponse
from app.models.stack_vulnerabilities_request import StackVulnerabilitiesRequest
from app.config import TASK_EXPIRATION
from app.tasks.asm.stack_vulnerabilities import run_stack_vulnerabilities
from app.utils.response_utils import create_task_response

logger = logging.getLogger(__name__)

router = APIRouter()


def _get_paginated_stack_vulnerabilities_results(
    job_id: str, 
    operation: Dict[str, Any], 
    page: int = 1, 
    records_per_page: int = 100
) -> TaskResponse:
    """
    Helper function to get paginated stack vulnerabilities results.
    
    Args:
        job_id: The job ID
        operation: The operation data
        page: Page number
        records_per_page: Records per page
        
    Returns:
        TaskResponse with paginated results
    """
    from app.db.operations import get_stack_vulnerabilities_chunk
    
    response = create_task_response(operation)
    
    if not response.results:
        response.results = {}
    
    # Base statistics
    base_results = {
        "statistics": operation.get("structured_output", {}).get("statistics", {}),
        "pagination": {
            "page": page,
            "records_per_page": records_per_page
        }
    }
    
    # Standard chunked pagination
    chunk_number = page - 1
    chunk = get_stack_vulnerabilities_chunk(job_id, chunk_number)
    
    if not chunk:
        stack_vulnerabilities = []
        total_records = 0
    else:
        stack_vulnerabilities = chunk.get("data", [])
        total_records = len(stack_vulnerabilities)
    
    limited_results = stack_vulnerabilities[:records_per_page]
    total_pages = ceil(total_records / records_per_page) if total_records > 0 else 1
    
    response.results.update({
        **base_results,
        "stack_vulnerabilities": limited_results,
        "pagination": {
            **base_results["pagination"],
            "total_records": total_records,
            "total_pages": total_pages
        }
    })
    
    return response


def _handle_completed_stack_vulnerabilities_operation(
    job_id: str,
    page: int = 1,
    records_per_page: int = 100
) -> TaskResponse:
    """
    Helper function to handle completed operations with pagination.
    
    Args:
        job_id: The job ID
        page: Page number
        records_per_page: Records per page
        
    Returns:
        TaskResponse with paginated results
    """
    operation = operations.get_operation(job_id)
    if not operation:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Operation {job_id} not found")
    
    return _get_paginated_stack_vulnerabilities_results(job_id, operation, page, records_per_page)


def validate_stack_vulns_query_params(
    domain: Optional[str] = Query(None, description="Domain name to analyze for stack vulnerabilities"),
    cpe: Optional[str] = Query(None, description="CPE string to analyze for vulnerabilities"),
    job_id: Optional[str] = Query(None, description="Job ID to check status"),
    ignore_cache: bool = Query(False, description="Force fresh analysis ignoring cache"),
    page: int = Query(1, ge=1, description="Page number for pagination"),
    records_per_page: int = Query(100, ge=1, le=1000, description="Number of records per page")
) -> Tuple[Optional[str], Optional[str], Optional[str], bool, int, int]:
    """Validate query parameters for stack vulnerabilities endpoint"""
    
    # If job_id is provided, ignore other parameters for status check
    if job_id:
        return None, None, job_id, ignore_cache, page, records_per_page
    
    # Validate that either domain or cpe is provided, but not both
    if not domain and not cpe:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Either 'domain' or 'cpe' parameter must be provided"
        )
    
    if domain and cpe:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Only one of 'domain' or 'cpe' parameters should be provided"
        )
    
    # Basic domain validation
    if domain:
        import re
        domain_pattern = re.compile(
            r'^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)*[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?$'
        )
        if not domain_pattern.match(domain):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid domain format: {domain}"
            )
    
    # Basic CPE validation
    if cpe:
        if not cpe.startswith('cpe:2.3:'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"CPE must start with 'cpe:2.3:': {cpe}"
            )
        
        parts = cpe.split(':')
        if len(parts) != 13:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"CPE must have 13 colon-separated components: {cpe}"
            )
    
    return domain, cpe, job_id, ignore_cache, page, records_per_page


@router.post("/stack-vulns", response_model=ExecutionResponse)
async def post_stack_vulnerabilities(request: StackVulnerabilitiesRequest) -> ExecutionResponse:
    """
    Start stack vulnerabilities analysis for a domain or CPE.

    **Request Body:**
    - `domain`: Domain to analyze tech stack from previous detection results
    - `cpe`: CPE string to directly analyze for vulnerabilities

    **Requirements:**
    - Either domain or cpe must be provided
    - For domain analysis: Previous tech detection results must exist
    - CPE format: cpe:2.3:part:vendor:product:version:update:edition:language:sw_edition:target_sw:target_hw:other
    """
    try:
        # Extract domain and cpe from request
        domain = request.domain
        cpe = request.cpe
        # Start a new task
        logger.info(f"Starting new stack vulnerabilities analysis for domain={domain}, cpe={cpe}")

        # Create operation in MongoDB
        job_id = operations.create_operation(
            feature="stack_vulnerabilities",
            parameters={"domain": domain, "cpe": cpe}
        )

        # Start the task - pass the job_id to the Celery task
        run_stack_vulnerabilities.delay(domain=domain, cpe=cpe, job_id=job_id)

        # Return lightweight execution response
        return ExecutionResponse(
            job_id=job_id,
            status=TaskStatus.PENDING,
            feature="stack_vulnerabilities",
            created_at=datetime.now()
        )

    except Exception as e:
        logger.exception(f"Error in stack vulnerabilities POST endpoint: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error starting task: {str(e)}"
        )


 