"""
ASM Stack Vulnerabilities Router
"""
from fastapi import APIRouter, HTTPException, status, Query, Depends
from typing import Optional, Dict, Any, Tuple
from datetime import datetime
from math import ceil
import logging

from app.db import operations
from app.models.task import TaskStatus, TaskResponse
from app.config import TASK_EXPIRATION
from app.tasks.asm.stack_vulnerabilities import run_stack_vulnerabilities
from app.utils.response_utils import create_task_response

logger = logging.getLogger(__name__)

router = APIRouter()


def _get_paginated_stack_vulnerabilities_results(
    job_id: str, 
    operation: Dict[str, Any], 
    page: int = 1, 
    records_per_page: int = 100
) -> TaskResponse:
    """
    Helper function to get paginated stack vulnerabilities results.
    
    Args:
        job_id: The job ID
        operation: The operation data
        page: Page number
        records_per_page: Records per page
        
    Returns:
        TaskResponse with paginated results
    """
    from app.db.operations import get_stack_vulnerabilities_chunk
    
    response = create_task_response(operation)
    
    if not response.results:
        response.results = {}
    
    # Base statistics
    base_results = {
        "statistics": operation.get("structured_output", {}).get("statistics", {}),
        "pagination": {
            "page": page,
            "records_per_page": records_per_page
        }
    }
    
    # Standard chunked pagination
    chunk_number = page - 1
    chunk = get_stack_vulnerabilities_chunk(job_id, chunk_number)
    
    if not chunk:
        stack_vulnerabilities = []
        total_records = 0
    else:
        stack_vulnerabilities = chunk.get("data", [])
        total_records = len(stack_vulnerabilities)
    
    limited_results = stack_vulnerabilities[:records_per_page]
    total_pages = ceil(total_records / records_per_page) if total_records > 0 else 1
    
    response.results.update({
        **base_results,
        "stack_vulnerabilities": limited_results,
        "pagination": {
            **base_results["pagination"],
            "total_records": total_records,
            "total_pages": total_pages
        }
    })
    
    return response


def _handle_completed_stack_vulnerabilities_operation(
    job_id: str,
    page: int = 1,
    records_per_page: int = 100
) -> TaskResponse:
    """
    Helper function to handle completed operations with pagination.
    
    Args:
        job_id: The job ID
        page: Page number
        records_per_page: Records per page
        
    Returns:
        TaskResponse with paginated results
    """
    operation = operations.get_operation(job_id)
    if not operation:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Operation {job_id} not found")
    
    return _get_paginated_stack_vulnerabilities_results(job_id, operation, page, records_per_page)


def validate_stack_vulns_query_params(
    domain: Optional[str] = Query(None, description="Domain name to analyze for stack vulnerabilities"),
    cpe: Optional[str] = Query(None, description="CPE string to analyze for vulnerabilities"),
    job_id: Optional[str] = Query(None, description="Job ID to check status"),
    ignore_cache: bool = Query(False, description="Force fresh analysis ignoring cache"),
    page: int = Query(1, ge=1, description="Page number for pagination"),
    records_per_page: int = Query(100, ge=1, le=1000, description="Number of records per page")
) -> Tuple[Optional[str], Optional[str], Optional[str], bool, int, int]:
    """Validate query parameters for stack vulnerabilities endpoint"""
    
    # If job_id is provided, ignore other parameters for status check
    if job_id:
        return None, None, job_id, ignore_cache, page, records_per_page
    
    # Validate that either domain or cpe is provided, but not both
    if not domain and not cpe:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Either 'domain' or 'cpe' parameter must be provided"
        )
    
    if domain and cpe:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Only one of 'domain' or 'cpe' parameters should be provided"
        )
    
    # Basic domain validation
    if domain:
        import re
        domain_pattern = re.compile(
            r'^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)*[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?$'
        )
        if not domain_pattern.match(domain):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid domain format: {domain}"
            )
    
    # Basic CPE validation
    if cpe:
        if not cpe.startswith('cpe:2.3:'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"CPE must start with 'cpe:2.3:': {cpe}"
            )
        
        parts = cpe.split(':')
        if len(parts) != 13:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"CPE must have 13 colon-separated components: {cpe}"
            )
    
    return domain, cpe, job_id, ignore_cache, page, records_per_page


@router.get("/stack-vulns", response_model=TaskResponse)
async def get_stack_vulnerabilities(
    validated_params: Tuple[Optional[str], Optional[str], Optional[str], bool, int, int] = Depends(validate_stack_vulns_query_params)
) -> TaskResponse:
    """
    Get stack vulnerabilities analysis results for a domain or CPE, or check job status.
    
    **Options:**
    1. **Check Status**: Provide only `job_id` to check analysis status
    2. **Domain Analysis**: Provide `domain` to analyze tech stack from previous detection results
    3. **CPE Analysis**: Provide `cpe` to directly analyze a specific CPE for vulnerabilities
    
    **Pagination:**
    - `page`: Page number (default: 1)
    - `records_per_page`: Records per page (default: 100, max: 1000)
    
    **Cache Behavior:**
    - Results are cached for 30 days by default
    - Use `ignore_cache=true` to force fresh analysis
    
    **Requirements:**
    - For domain analysis: Previous tech detection results must exist
    - CPE format: cpe:2.3:part:vendor:product:version:update:edition:language:sw_edition:target_sw:target_hw:other
    """
    domain, cpe, job_id, ignore_cache, page, records_per_page = validated_params
    
    try:
        # Case 1: Check existing job status
        if job_id and not domain and not cpe:
            operation_status = operations.get_operation_status(job_id)
            if not operation_status:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Job {job_id} not found")

            # If the operation is completed, return paginated results
            if operation_status["status"] in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.KILLED]:
                if operation_status["status"] == TaskStatus.COMPLETED:
                    return _handle_completed_stack_vulnerabilities_operation(job_id, page, records_per_page)
                else:
                    # For failed/killed operations, return the status without pagination
                    operation = operations.get_operation(job_id)
                    return create_task_response(operation)
            else:
                # For running operations, use the status info which includes real-time progress
                return create_task_response(operation_status)

        # Case 2: Get or start analysis for domain or CPE
        if not domain and not cpe:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Either 'domain', 'cpe', or 'job_id' parameter must be provided"
            )

        # Create parameters for cache checking
        parameters = {}
        if domain:
            parameters["domain"] = domain
        if cpe:
            parameters["cpe"] = cpe

        # First, always check for running or pending tasks, regardless of ignore_cache
        running_operation = operations.get_running_operation("stack_vulnerabilities", parameters)
        
        if running_operation:
            job_id = running_operation['_id']
            logger.info(f"Found existing running stack vulnerabilities task {job_id}")
            return create_task_response(running_operation)
        
        # Then, check for existing recent results if not ignoring cache
        if not ignore_cache:
            # Calculate cutoff time
            cutoff_time = datetime.now() - TASK_EXPIRATION.get("stack_vulnerabilities", TASK_EXPIRATION["default"])
            
            recent_operation = operations.get_recent_operation(
                feature="stack_vulnerabilities",
                parameters=parameters,
                cutoff_time=cutoff_time
            )
            
            if recent_operation:
                response = _get_paginated_stack_vulnerabilities_results(recent_operation['_id'], recent_operation, page, records_per_page)
                response.cache_hit = True
                return response

        # No recent results found, start a new task
        if domain:
            logger.info(f"Starting new stack vulnerabilities analysis for domain {domain}")
        else:
            logger.info(f"Starting new stack vulnerabilities analysis for CPE {cpe}")

        # Create operation in MongoDB
        job_id = operations.create_operation(
            feature="stack_vulnerabilities",
            parameters=parameters
        )
        
        # Start the task - pass the job_id to the Celery task
        run_stack_vulnerabilities.delay(domain=domain, cpe=cpe, job_id=job_id)
        
        # Return response with the job_id
        return TaskResponse(
            job_id=job_id,
            status=TaskStatus.PENDING,
            feature="stack_vulnerabilities",
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error in stack vulnerabilities GET endpoint: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error starting task: {str(e)}"
        )


 