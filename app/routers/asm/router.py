"""
Main router for Attack Surface Mapping (ASM) routes.
"""
from fastapi import APIRouter

from app.routers.asm.subdomains import router as subdomains_router
from app.routers.asm.public_ips import router as public_ips_router
from app.routers.asm.webservers import router as webservers_router
from app.routers.asm.tech_detection import router as tech_detection_router
from app.routers.asm.stack_vulnerabilities import router as stack_vulnerabilities_router
from app.routers.asm.nuclei_scan import router as nuclei_scan_router

# Create main ASM router
router = APIRouter()

# Include all ASM routes
router.include_router(subdomains_router)
router.include_router(public_ips_router)
router.include_router(webservers_router)
router.include_router(tech_detection_router)
router.include_router(stack_vulnerabilities_router)
router.include_router(nuclei_scan_router)
