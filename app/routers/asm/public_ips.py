"""
Public IP search route.
"""
import logging
from typing import <PERSON><PERSON>, <PERSON><PERSON>
from datetime import datetime

from fastapi import APIRouter, HTTPException, status, Query

from app.db import operations
from app.models.task import TaskStatus, TaskResponse
from app.models.public_ips_request import PublicIpsRequest
from app.tasks.asm.public_ip_search import run_public_ip_search
from app.utils.response_utils import create_task_response

logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/public_ips", response_model=TaskResponse)
async def post_public_ips(
    request: PublicIpsRequest,
    job_id: Optional[str] = Query(None, description="Task ID from a previous request to check status")
):
    """Start public IPs discovery for a domain

    Request Body:
    {
        "domain": "example.com",
        "depth": 0  # Number of pages to fetch from Shodan API (0 for unlimited)
    }
    """

    try:
        # If job_id is provided, check if operation exists
        if job_id:
            operation_status = operations.get_operation_status(job_id)
            if not operation_status:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Task {job_id} not found")
            return create_task_response(operation_status)

        # Extract domain and depth from request
        domain = request.domain
        depth = request.depth

        # Start a new task
        logger.info(f"Starting new public IP search for domain {domain}")

        # Create operation in MongoDB
        job_id = operations.create_operation(
            feature="public_ips",
            parameters={"domain": domain, "depth": depth}
        )

        # Start the task - pass the job_id to the Celery task
        run_public_ip_search.delay(domain, job_id, depth)

        # Return response with the job_id
        return TaskResponse(
            job_id=job_id,
            status=TaskStatus.PENDING,
            feature="public_ips",
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error starting public IP search task: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error starting task: {str(e)}",
        )
