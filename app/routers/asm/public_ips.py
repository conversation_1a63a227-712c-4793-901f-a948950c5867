"""
Public IP search route.
"""
import logging
from typing import Op<PERSON>, <PERSON><PERSON>
from datetime import datetime

from fastapi import APIRouter, HTTPException, status, Depends, Query

from app.db import operations
from app.models.task import TaskStatus, TaskResponse
from app.tasks.asm.public_ip_search import run_public_ip_search
from app.validators import validate_domain_param
from app.utils.response_utils import create_task_response
from app.config import TASK_EXPIRATION

logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/public_ips", response_model=TaskResponse)
async def get_public_ips(
    validated_params: Tuple[Optional[str], Optional[str], bool, bool] = Depends(validate_domain_param),
    depth: int = Query(0, description="Number of pages to fetch from Shodan API (0 for unlimited)")
):
    """Search for public IPs associated with a domain using Shodan, Censys, etc.

    Set ignore_cache=true to bypass stored results and force a new scan
    """
    domain, job_id, ignore_cache, _ = validated_params

    # If job_id is provided, check if operation exists
    if job_id:
        # Get operation status (combines database and Celery status)
        operation_status = operations.get_operation_status(job_id)
        if not operation_status:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Task {job_id} not found")

        # If the operation is completed or failed, get the full operation with results
        if operation_status["status"] in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.KILLED]:
            operation = operations.get_operation(job_id)
        else:
            # For running operations, use the status info which includes real-time progress
            operation = operation_status

        return create_task_response(operation)

    try:
        # First, always check for running or pending tasks, regardless of ignore_cache
        running_operation = operations.get_running_operation("public_ips", {"domain": domain})

        if running_operation:
            # Extract the job_id from the MongoDB _id field
            job_id = running_operation['_id']
            logger.info(f"Found existing running task {job_id} for domain {domain}")
            return create_task_response(running_operation)

        # Then, check for completed or failed tasks if ignore_cache is not set
        if not ignore_cache:
            # Calculate cutoff time
            cutoff_time = datetime.now() - TASK_EXPIRATION.get("public_ips", TASK_EXPIRATION["default"])

            # Find recent operation
            recent_operation = operations.get_recent_operation(
                feature="public_ips",
                parameters={"domain": domain},
                cutoff_time=cutoff_time
            )

            if recent_operation:
                # Create response with cache_hit flag
                response = create_task_response(recent_operation)
                response.cache_hit = True
                return response

        # No recent results found, start a new task
        logger.info(f"Starting new public IP search for domain {domain}")

        # Create operation in MongoDB
        job_id = operations.create_operation(
            feature="public_ips",
            parameters={"domain": domain}
        )

        # Start the task - pass the job_id to the Celery task
        run_public_ip_search.delay(domain, job_id, depth)

        # Return response with the job_id
        return TaskResponse(
            job_id=job_id,
            status=TaskStatus.PENDING,
            feature="public_ips",
        )
    except Exception as e:
        logger.exception(f"Error starting public IP search task: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error starting task: {str(e)}",
        )
