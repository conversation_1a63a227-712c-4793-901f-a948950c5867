"""
ASM Nuclei Vulnerability Scan Router
"""
from fastapi import APIRouter, HTTPException, status
from datetime import datetime
import logging

from app.db import operations
from app.models.nuclei_scan_request import NucleiScanRequest
from app.models.task import TaskStatus, ExecutionResponse
from app.tasks.asm.nuclei_scan import run_nuclei_scan

logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/scan", response_model=ExecutionResponse)
async def nuclei_vulnerability_scan(request: NucleiScanRequest) -> ExecutionResponse:
    """
    Run nuclei vulnerability scan on provided targets with specified CVE IDs.
    
    **Request Body:**
    - `cve_ids`: List of CVE IDs to scan for (e.g., ["CVE-2021-44228", "CVE-2023-41221"])
    - `targets`: List of targets to scan (domains, subdomains, or IP addresses)
    - `distributed`: Boolean flag for distributed scanning using axiom-shell (default: false)
    
    **Execution Modes:**
    1. **Single Execution** (distributed=false): Run nuclei locally on the worker
    2. **Distributed Execution** (distributed=true): Use axiom-shell container for cloud-distributed scanning
    
    **Cache Behavior:**
    - Results are cached for 6 hours by default
    - Use `ignore_cache=true` to force fresh scan
    - Cache key is based on CVE IDs, targets, and distributed flag
    
    **Response:**
    - Returns job details with current status
    - Use GET endpoint with job_id to check progress and get final results
    - Extracted fields: template-id, type, host, port, url, matched-at, extracted-results, request, response, ip, timestamp, curl-command, matcher-status
    
    **Requirements:**
    - For distributed scanning: axiom-shell container must be running
    - nuclei templates must be available at /root/nuclei-templates/
    """
    try:
        # Create parameters for cache checking
        parameters = {
            "cve_ids": request.cve_ids,
            "targets": request.targets,
            "distributed": request.distributed
        }

        # Start a new task
        logger.info(f"Starting new nuclei scan for {len(request.targets)} targets with {len(request.cve_ids)} CVE IDs (distributed: {request.distributed})")

        # Create operation in MongoDB
        job_id = operations.create_operation(
            feature="nuclei_scan",
            parameters=parameters
        )
        
        # Start the task - pass the job_id to the Celery task
        run_nuclei_scan.delay(
            cve_ids=request.cve_ids,
            targets=request.targets,
            distributed=request.distributed,
            job_id=job_id
        )
        
        # Return lightweight execution response
        return ExecutionResponse(
            job_id=job_id,
            status=TaskStatus.PENDING,
            feature="nuclei_scan",
            created_at=datetime.now()
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error in nuclei scan POST endpoint: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error starting task: {str(e)}"
        ) 