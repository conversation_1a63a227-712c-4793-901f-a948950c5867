"""
ASM Nuclei Vulnerability Scan Router
"""
from fastapi import APIRouter, HTTPException, status, Depends
from typing import Optional, Dict, Any, Tu<PERSON>
from datetime import datetime
import logging

from app.db import operations
from app.models.nuclei_scan_request import NucleiScanRequest
from app.models.task import TaskStatus, TaskResponse
from app.config import TASK_EXPIRATION
from app.tasks.asm.nuclei_scan import run_nuclei_scan
from app.validators import validate_nuclei_scan_query_params
from app.utils.response_utils import create_task_response

logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/scan", response_model=TaskResponse)
async def nuclei_vulnerability_scan(
    request: NucleiScanRequest,
    validated_params: Tuple[Optional[str], bool] = Depends(validate_nuclei_scan_query_params)
) -> TaskResponse:
    """
    Run nuclei vulnerability scan on provided targets with specified CVE IDs.
    
    **Request Body:**
    - `cve_ids`: List of CVE IDs to scan for (e.g., ["CVE-2021-44228", "CVE-2023-41221"])
    - `targets`: List of targets to scan (domains, subdomains, or IP addresses)
    - `distributed`: Boolean flag for distributed scanning using axiom-shell (default: false)
    
    **Execution Modes:**
    1. **Single Execution** (distributed=false): Run nuclei locally on the worker
    2. **Distributed Execution** (distributed=true): Use axiom-shell container for cloud-distributed scanning
    
    **Cache Behavior:**
    - Results are cached for 6 hours by default
    - Use `ignore_cache=true` to force fresh scan
    - Cache key is based on CVE IDs, targets, and distributed flag
    
    **Response:**
    - Returns job details with current status
    - Use GET endpoint with job_id to check progress and get final results
    - Extracted fields: template-id, type, host, port, url, matched-at, extracted-results, request, response, ip, timestamp, curl-command, matcher-status
    
    **Requirements:**
    - For distributed scanning: axiom-shell container must be running
    - nuclei templates must be available at /root/nuclei-templates/
    """
    job_id, ignore_cache = validated_params
    
    # If job_id is provided, check if operation exists
    if job_id:
        operation_status = operations.get_operation_status(job_id)
        if not operation_status:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Job {job_id} not found")

        # If the operation is completed or failed, get the full operation with results
        if operation_status["status"] in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.KILLED]:
            operation = operations.get_operation(job_id)
        else:
            # For running operations, use the status info which includes real-time progress
            operation = operation_status

        return create_task_response(operation)

    try:
        # Create parameters for cache checking
        parameters = {
            "cve_ids": request.cve_ids,
            "targets": request.targets,
            "distributed": request.distributed
        }

        # First, always check for running or pending tasks, regardless of ignore_cache
        running_operation = operations.get_running_operation("nuclei_scan", parameters)
        
        if running_operation:
            job_id = running_operation['_id']
            logger.info(f"Found existing running nuclei scan task {job_id}")
            return create_task_response(running_operation)
        
        # Then, check for existing recent results if not ignoring cache
        if not ignore_cache:
            # Calculate cutoff time
            cutoff_time = datetime.now() - TASK_EXPIRATION.get("nuclei_scan", TASK_EXPIRATION["default"])
            
            recent_operation = operations.get_recent_operation(
                feature="nuclei_scan",
                parameters=parameters,
                cutoff_time=cutoff_time
            )
            
            if recent_operation:
                response = create_task_response(recent_operation)
                response.cache_hit = True
                return response

        # No recent results found, start a new task
        logger.info(f"Starting new nuclei scan for {len(request.targets)} targets with {len(request.cve_ids)} CVE IDs (distributed: {request.distributed})")

        # Create operation in MongoDB
        job_id = operations.create_operation(
            feature="nuclei_scan",
            parameters=parameters
        )
        
        # Start the task - pass the job_id to the Celery task
        run_nuclei_scan.delay(
            cve_ids=request.cve_ids,
            targets=request.targets,
            distributed=request.distributed,
            job_id=job_id
        )
        
        # Return response with the job_id
        return TaskResponse(
            job_id=job_id,
            status=TaskStatus.PENDING,
            feature="nuclei_scan",
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error in nuclei scan POST endpoint: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error starting task: {str(e)}"
        ) 