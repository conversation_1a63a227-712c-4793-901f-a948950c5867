"""
Webserver discovery route.
"""
import logging
from datetime import datetime

from fastapi import APIRouter, HTTPException, status, Query

from app.db import operations
from app.models.task import TaskStatus, ExecutionResponse
from app.models.webserver_request import WebserverDiscoveryPostRequest, WebserverDiscoveryRequest
from app.tasks.asm.webserver_discovery import run_webserver_discovery

logger = logging.getLogger(__name__)

router = APIRouter()



@router.post("/webservers", response_model=ExecutionResponse)
async def post_webservers(request: WebserverDiscoveryRequest) -> ExecutionResponse:
    """Start webserver discovery scan using stored subdomain data

    Request Body:
    {
        "domain": "example.com",
        "ignore_cache": false
    }
    """
    try:
        # Extract domain from request
        domain = request.domain

        # Start a new task
        logger.info(f"Starting new webserver discovery for domain {domain}")

        # Create operation in MongoDB
        job_id = operations.create_operation(
            feature="webserver_discovery",
            parameters={"domain": domain}
        )

        # Start the task - pass the job_id to the Celery task
        task = run_webserver_discovery.delay(domain=domain, subdomains=None, job_id=job_id, chunk_size=50)
        logger.info(f"Started Celery task {task.id} for job {job_id}")

        # Return lightweight execution response
        return ExecutionResponse(
            job_id=job_id,
            status=TaskStatus.PENDING,
            feature="webserver_discovery",
            created_at=datetime.now()
        )
    except Exception as e:
        logger.exception(f"Error starting webserver discovery task: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error starting task: {str(e)}",
        )

@router.post("/webservers_custom", response_model=ExecutionResponse)
async def discover_webservers_custom(request: WebserverDiscoveryPostRequest) -> ExecutionResponse:
    """
    Start webserver discovery for a provided list of subdomains.

    Request Body:
    {
        "subdomains": ["api.example.com", "blog.example.com", "admin.example.com"]
    }
    """
    try:
        # Extract subdomains from request
        subdomains = request.subdomains

        # Start a new task
        logger.info(f"Starting new webserver discovery for {len(subdomains)} subdomains")

        # Create operation in MongoDB
        job_id = operations.create_operation(
            feature="webserver_discovery",
            parameters={"subdomains": subdomains}
        )

        # Start the task - pass the job_id to the Celery task
        task = run_webserver_discovery.delay(domain=None, subdomains=subdomains, job_id=job_id, chunk_size=50)
        logger.info(f"Started Celery task {task.id} for job {job_id}")

        # Return lightweight execution response
        return ExecutionResponse(
            job_id=job_id,
            status=TaskStatus.PENDING,
            feature="webserver_discovery",
            created_at=datetime.now()
        )
    except Exception as e:
        logger.exception(f"Error starting webserver discovery task: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error starting task: {str(e)}",
        ) 