"""
Webserver discovery route.
"""
import logging
from typing import Op<PERSON>, Tuple, List, Dict, Any
from datetime import datetime
from math import ceil

from fastapi import APIRouter, HTTPException, status, Depends, Query
from pydantic import ValidationError

from app.db import operations
from app.models.task import TaskStatus, TaskResponse
from app.models.webserver_request import WebserverDiscoveryPostRequest
from app.tasks.asm.webserver_discovery import run_webserver_discovery
from app.utils.response_utils import create_task_response
from app.config import TASK_EXPIRATION

logger = logging.getLogger(__name__)

router = APIRouter()


async def _get_detailed_results(job_id: str, operation_status: Dict[str, Any], page: int, records_per_page: int):
    """
    Internal function to get detailed paginated results for a completed webserver discovery task.
    """
    try:
        # Get statistics from operation
        structured_output = operation_status.get("structured_output", {})
        statistics = structured_output.get("statistics", {})

        if not statistics:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Task {job_id} does not have statistics data"
            )

        # Calculate pagination
        total_records = statistics.get("total_webservers", 0)
        chunk_size = statistics.get("chunk_size", 50)
        total_chunks = statistics.get("total_chunks", 0)

        # Calculate which chunks we need for this page
        records_per_chunk = chunk_size
        start_record = (page - 1) * records_per_page
        end_record = start_record + records_per_page

        start_chunk = start_record // records_per_chunk
        end_chunk = min(ceil(end_record / records_per_chunk), total_chunks)

        # Get chunks
        chunks_to_fetch = list(range(start_chunk, end_chunk))
        records = []

        for chunk_number in chunks_to_fetch:
            chunk = operations.get_webserver_discovery_chunk(job_id, chunk_number)

            if chunk and "data" in chunk:
                # Calculate slice for first and last chunks
                if chunk_number == start_chunk:
                    start_idx = start_record % records_per_chunk
                else:
                    start_idx = 0

                if chunk_number == end_chunk - 1:
                    end_idx = min(end_record % records_per_chunk or records_per_chunk, len(chunk["data"]))
                else:
                    end_idx = len(chunk["data"])

                records.extend(chunk["data"][start_idx:end_idx])

                # Stop if we have enough records
                if len(records) >= records_per_page:
                    records = records[:records_per_page]
                    break
            else:
                logger.warning(f"Chunk {chunk_number} not found or has no data for job_id {job_id}")

        # Calculate total pages
        total_pages = ceil(total_records / records_per_page) if total_records > 0 else 1

        # Create standard TaskResponse with results populated
        response = create_task_response(operation_status)
        
        # Populate the results field with detailed data
        if not response.results:
            response.results = {}
            
        response.results.update({
            "statistics": statistics,
            "webservers": records,
            "pagination": {
                "total_records": total_records,
                "page": page,
                "total_pages": total_pages,
                "records_per_page": records_per_page
            }
        })
        
        return response

    except HTTPException:
        # Re-raise HTTPExceptions (like 400 Bad Request) without modification
        raise
    except Exception as e:
        logger.exception(f"Error getting detailed results for task {job_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving detailed results: {str(e)}"
        )


@router.get("/webservers", response_model=TaskResponse)
async def get_webservers(
    domain: str = Query(None, description="Target domain to use previously discovered subdomains (e.g., example.com)"),
    job_id: Optional[str] = Query(None, description="Task ID from a previous request to check status"),
    ignore_cache: bool = Query(False, description="Force a new scan, bypassing stored results"),
    page: int = Query(1, description="Page number for detailed results", ge=1),
    records_per_page: int = Query(50, description="Number of records per page for detailed results", ge=10, le=500)
):
    """Get webserver discovery results using stored subdomain data or check task status

    This unified endpoint handles all webserver discovery operations:
    - Start new webserver discovery scans
    - Check status of existing scans  
    - Return detailed paginated results for completed scans

    Query Parameters:
    - domain: Target domain to use previously discovered subdomains (e.g., example.com)
    - job_id: Task ID from a previous request to check status
    - ignore_cache: Force a new scan, bypassing stored results (default: false)
    - page: Page number for detailed results (when task is completed)
    - records_per_page: Number of records per page for detailed results (when task is completed)

    Either domain or job_id must be provided.
    """
    
    # If job_id is provided, check if operation exists
    if job_id:
        # Get operation status (combines database and Celery status)
        operation_status = operations.get_operation_status(job_id)
        if not operation_status:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Task {job_id} not found")

        # Check if operation is for webserver_discovery
        if operation_status.get("feature") != "webserver_discovery":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Task {job_id} is not a webserver discovery"
            )

        # If task is completed, return paginated detailed results
        if operation_status.get("status") == TaskStatus.COMPLETED:
            return await _get_detailed_results(job_id, operation_status, page, records_per_page)
        
        # Otherwise return basic status/summary
        return create_task_response(operation_status)

    # For GET requests, domain is required if no job_id
    if not domain:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Either 'domain' or 'job_id' parameter must be provided for GET request"
        )

    try:
        # Create parameters for cache checking
        parameters = {"domain": domain}

        # First, always check for running or pending tasks, regardless of ignore_cache
        running_operation = operations.get_running_operation("webserver_discovery", parameters)

        if running_operation:
            # Extract the job_id from the MongoDB _id field
            job_id = running_operation['_id']
            logger.info(f"Found existing running webserver discovery task {job_id}")
            return create_task_response(running_operation)

        # Then, check for completed or failed tasks if ignore_cache is not set
        if not ignore_cache:
            # Calculate cutoff time
            cutoff_time = datetime.now() - TASK_EXPIRATION.get("webserver_discovery", TASK_EXPIRATION["default"])

            # Find recent operation
            recent_operation = operations.get_recent_operation(
                feature="webserver_discovery",
                parameters=parameters,
                cutoff_time=cutoff_time
            )

            if recent_operation:
                # If task is completed, return paginated detailed results
                if recent_operation.get("status") == TaskStatus.COMPLETED:
                    return await _get_detailed_results(recent_operation["_id"], recent_operation, page, records_per_page)
                
                # Create response with cache_hit flag
                response = create_task_response(recent_operation)
                response.cache_hit = True
                return response

        # No recent results found, start a new task
        logger.info(f"Starting new webserver discovery for domain {domain}")

        # Create operation in MongoDB
        job_id = operations.create_operation(
            feature="webserver_discovery",
            parameters=parameters
        )
        logger.info(f"Created operation {job_id} for webserver discovery")

        # Start the task - pass the job_id to the Celery task
        try:
            task = run_webserver_discovery.delay(domain=domain, subdomains=None, job_id=job_id, chunk_size=50)
            logger.info(f"Started Celery task {task.id} for job {job_id}")
        except Exception as task_error:
            logger.error(f"Failed to start Celery task: {task_error}")
            # Update operation with error
            operations.update_operation(
                job_id=job_id,
                status=TaskStatus.FAILED,
                error=f"Failed to start task: {str(task_error)}",
                end_time=datetime.now()
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to start task: {str(task_error)}",
            )

        # Return response with the job_id
        return TaskResponse(
            job_id=job_id,
            status=TaskStatus.PENDING,
            feature="webserver_discovery",
        )
    except Exception as e:
        logger.exception(f"Error starting webserver discovery task: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error starting task: {str(e)}",
        )

@router.post("/webservers", response_model=TaskResponse)
async def discover_webservers(
    request: WebserverDiscoveryPostRequest,
    job_id: Optional[str] = Query(None, description="Task ID from a previous request to check status"),
    ignore_cache: bool = Query(False, description="Force a new scan, bypassing stored results"),
    page: int = Query(1, description="Page number for detailed results", ge=1),
    records_per_page: int = Query(50, description="Number of records per page for detailed results", ge=10, le=500)
):
    """Discover web servers for a provided list of subdomains

    This unified endpoint handles all webserver discovery operations for custom subdomain lists:
    - Start new webserver discovery scans  
    - Check status of existing scans
    - Return detailed paginated results for completed scans

    Query Parameters:
    - job_id: Task ID from a previous request to check status
    - ignore_cache: Force a new scan, bypassing stored results (default: false)
    - page: Page number for detailed results (when task is completed)
    - records_per_page: Number of records per page for detailed results (when task is completed)

    Request Body:
    {
        "subdomains": ["api.example.com", "blog.example.com", "admin.example.com"]
    }
    """

    # If job_id is provided, check if operation exists
    if job_id:
        # Get operation status (combines database and Celery status)
        operation_status = operations.get_operation_status(job_id)
        if not operation_status:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Task {job_id} not found")

        # Check if operation is for webserver_discovery
        if operation_status.get("feature") != "webserver_discovery":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Task {job_id} is not a webserver discovery"
            )

        # If task is completed, return paginated detailed results
        if operation_status.get("status") == TaskStatus.COMPLETED:
            return await _get_detailed_results(job_id, operation_status, page, records_per_page)
        
        # Otherwise return basic status/summary
        return create_task_response(operation_status)

    try:
        # Create parameters for cache checking
        parameters = {"subdomains": request.subdomains}

        # First, always check for running or pending tasks, regardless of ignore_cache
        running_operation = operations.get_running_operation("webserver_discovery", parameters)

        if running_operation:
            # Extract the job_id from the MongoDB _id field
            job_id = running_operation['_id']
            logger.info(f"Found existing running webserver discovery task {job_id}")
            return create_task_response(running_operation)

        # Then, check for completed or failed tasks if ignore_cache is not set
        if not ignore_cache:
            # Calculate cutoff time
            cutoff_time = datetime.now() - TASK_EXPIRATION.get("webserver_discovery", TASK_EXPIRATION["default"])

            # Find recent operation
            recent_operation = operations.get_recent_operation(
                feature="webserver_discovery",
                parameters=parameters,
                cutoff_time=cutoff_time
            )

            if recent_operation:
                # If task is completed, return paginated detailed results
                if recent_operation.get("status") == TaskStatus.COMPLETED:
                    return await _get_detailed_results(recent_operation["_id"], recent_operation, page, records_per_page)
                
                # Create response with cache_hit flag
                response = create_task_response(recent_operation)
                response.cache_hit = True
                return response

        # No recent results found, start a new task
        logger.info(f"Starting new webserver discovery for {len(request.subdomains)} subdomains")

        # Create operation in MongoDB
        job_id = operations.create_operation(
            feature="webserver_discovery",
            parameters=parameters
        )
        logger.info(f"Created operation {job_id} for webserver discovery with {len(request.subdomains)} subdomains")

        # Start the task - pass the job_id to the Celery task
        try:
            task = run_webserver_discovery.delay(domain=None, subdomains=request.subdomains, job_id=job_id, chunk_size=50)
            logger.info(f"Started Celery task {task.id} for job {job_id}")
        except Exception as task_error:
            logger.error(f"Failed to start Celery task: {task_error}")
            # Update operation with error
            operations.update_operation(
                job_id=job_id,
                status=TaskStatus.FAILED,
                error=f"Failed to start task: {str(task_error)}",
                end_time=datetime.now()
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to start task: {str(task_error)}",
            )

        # Return response with the job_id
        return TaskResponse(
            job_id=job_id,
            status=TaskStatus.PENDING,
            feature="webserver_discovery",
        )
    except Exception as e:
        logger.exception(f"Error starting webserver discovery task: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error starting task: {str(e)}",
        ) 