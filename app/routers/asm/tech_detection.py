"""
ASM Technology Detection Router
"""
from fastapi import APIRouter, HTTPException, status, Query, Depends
from typing import Optional, Dict, Any, Tuple
from datetime import datetime
from math import ceil
import logging

from app.db import operations
from app.models.tech_detection_request import TechDetectionPostRequest
from app.models.task import TaskStatus, TaskResponse
from app.config import TASK_EXPIRATION
from app.tasks.asm.tech_detection import run_tech_detection
from app.validators import validate_tech_detection_query_params
from app.utils.response_utils import create_task_response

logger = logging.getLogger(__name__)

router = APIRouter()


def _get_paginated_tech_results(
    job_id: str, 
    operation: Dict[str, Any], 
    url: Optional[str] = None, 
    page: int = 1, 
    records_per_page: int = 100
) -> TaskResponse:
    """
    Helper function to get paginated technology detection results.
    
    Args:
        job_id: The job ID
        operation: The operation data
        url: Optional URL filter
        page: Page number
        records_per_page: Records per page
        
    Returns:
        TaskResponse with paginated results
    """
    from app.db.operations import get_tech_detection_chunk, get_tech_detection_results_by_url
    
    response = create_task_response(operation)
    
    if not response.results:
        response.results = {}
    
    # Base statistics
    base_results = {
        "statistics": operation.get("structured_output", {}).get("statistics", {}),
        "pagination": {
            "page": page,
            "records_per_page": records_per_page
        }
    }
    
    # URL-filtered results
    if url:
        all_results = get_tech_detection_results_by_url(job_id, url)
        
        # Apply pagination
        start_idx = (page - 1) * records_per_page
        end_idx = start_idx + records_per_page
        paginated_results = all_results[start_idx:end_idx]
        
        # Calculate total pages
        total_pages = ceil(len(all_results) / records_per_page) if len(all_results) > 0 else 1
        
        response.results.update({
            **base_results,
            "tech_detections": paginated_results,
            "pagination": {
                **base_results["pagination"],
                "total_records": len(all_results),
                "total_pages": total_pages
            },
            "url_filter": url
        })
    else:
        # Standard chunked pagination
        chunk_number = page - 1
        chunk = get_tech_detection_chunk(job_id, chunk_number)
        
        if not chunk:
            tech_detections = []
            total_records = 0
        else:
            tech_detections = chunk.get("data", [])
            total_records = len(tech_detections)
        
        limited_detections = tech_detections[:records_per_page]
        total_pages = ceil(total_records / records_per_page) if total_records > 0 else 1
        
        response.results.update({
            **base_results,
            "tech_detections": limited_detections,
            "pagination": {
                **base_results["pagination"],
                "total_records": total_records,
                "total_pages": total_pages
            }
        })
    
    return response


def _handle_completed_operation(
    job_id: str,
    url: Optional[str] = None,
    page: int = 1,
    records_per_page: int = 100
) -> TaskResponse:
    """
    Helper function to handle completed operations with pagination.
    
    Args:
        job_id: The job ID
        url: Optional URL filter
        page: Page number
        records_per_page: Records per page
        
    Returns:
        TaskResponse with paginated results
    """
    operation = operations.get_operation(job_id)
    return _get_paginated_tech_results(job_id, operation, url, page, records_per_page)


def _handle_cached_operation(
    recent_operation: Dict[str, Any],
    url: Optional[str] = None,
    page: int = 1,
    records_per_page: int = 100
) -> TaskResponse:
    """
    Helper function to handle cached operations.
    
    Args:
        recent_operation: The cached operation data
        url: Optional URL filter
        page: Page number
        records_per_page: Records per page
        
    Returns:
        TaskResponse with cache hit and optionally paginated results
    """
    response = create_task_response(recent_operation)
    response.cache_hit = True
    
    # If URL filter is provided, get filtered results
    if url:
        job_id = recent_operation['_id']
        response = _get_paginated_tech_results(job_id, recent_operation, url, page, records_per_page)
        response.cache_hit = True
        return response
    
    # If the task is completed and no URL filter, handle pagination
    if response.status == TaskStatus.COMPLETED:
        job_id = recent_operation['_id']
        response = _get_paginated_tech_results(job_id, recent_operation, None, page, records_per_page)
        response.cache_hit = True
    
    return response

@router.get("/tech-detection", response_model=TaskResponse)
async def get_tech_detection(
    validated_params: Tuple[Optional[str], Optional[str], bool] = Depends(validate_tech_detection_query_params),
    url: Optional[str] = Query(default=None, description="Filter results by specific URL"),
    page: int = Query(default=1, ge=1, description="Page number for results"),
    records_per_page: int = Query(default=100, ge=1, le=1000, description="Items per page for results")
) -> TaskResponse:
    """
    Get technology detection results using stored webserver URLs for a domain or check job status.
    
    **Options:**
    1. **Check Status**: Provide only `job_id` to check analysis status
    2. **Domain Analysis**: Provide `domain` to analyze webserver URLs from previous discovery
    3. **URL Filtering**: Add `url` parameter to filter results for specific URL
    4. **Pagination**: Use `page` and `records_per_page` parameters for paginated results when job is completed
    
    **Cache Behavior:**
    - Results are cached for 30 days by default
    - Use `ignore_cache=true` to force fresh analysis
    
    **Requirements:**
    - For domain analysis: Previous webserver discovery results must exist
    - If no webserver URLs found, returns error with suggestion to run webserver discovery
    """
    domain, job_id, ignore_cache = validated_params
    
    try:
        # Case 1: Check existing job status
        if job_id and not domain:
            operation_status = operations.get_operation_status(job_id)
            if not operation_status:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Job {job_id} not found")

            # If the operation is completed, return paginated results
            if operation_status["status"] == TaskStatus.COMPLETED:
                return _handle_completed_operation(job_id, url, page, records_per_page)
            
            # For running, failed, or killed operations, return standard status
            elif operation_status["status"] in [TaskStatus.FAILED, TaskStatus.KILLED]:
                operation = operations.get_operation(job_id)
            else:
                # For running operations, use the status info which includes real-time progress
                operation = operation_status

            return create_task_response(operation)

        # Case 2: Get or start analysis for domain
        if not domain:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Either 'domain' or 'job_id' parameter must be provided"
            )

        # Create parameters for cache checking
        parameters = {"domain": domain}

        # First, always check for running or pending tasks, regardless of ignore_cache
        running_operation = operations.get_running_operation("tech_detection", parameters)
        
        if running_operation:
            job_id = running_operation['_id']
            logger.info(f"Found existing running tech detection task {job_id} for domain {domain}")
            return create_task_response(running_operation)
        
        # Then, check for existing recent results if not ignoring cache
        if not ignore_cache:
            # Calculate cutoff time
            cutoff_time = datetime.now() - TASK_EXPIRATION.get("tech_detection", TASK_EXPIRATION["default"])
            
            recent_operation = operations.get_recent_operation(
                feature="tech_detection",
                parameters=parameters,
                cutoff_time=cutoff_time
            )
            
            if recent_operation:
                return _handle_cached_operation(recent_operation, url, page, records_per_page)

        # No recent results found, start a new task
        logger.info(f"Starting new technology detection for domain {domain}")

        # Create operation in MongoDB
        job_id = operations.create_operation(
            feature="tech_detection",
            parameters=parameters
        )
        
        # Start the task - pass the job_id to the Celery task
        run_tech_detection.delay(domain=domain, job_id=job_id)
        
        # Return response with the job_id
        return TaskResponse(
            job_id=job_id,
            status=TaskStatus.PENDING,
            feature="tech_detection",
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error in tech detection GET endpoint: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error starting task: {str(e)}"
        )

@router.post("/tech-detection_custom", response_model=TaskResponse)
async def post_tech_detection_custom(
    request: TechDetectionPostRequest,
    validated_params: Tuple[Optional[str], Optional[str], bool] = Depends(validate_tech_detection_query_params),
    url: Optional[str] = Query(default=None, description="Filter results by specific URL"),
    page: int = Query(default=1, ge=1, description="Page number for results"),
    records_per_page: int = Query(default=100, ge=1, le=1000, description="Items per page for results")
) -> TaskResponse:
    """
    Run technology detection analysis on provided URLs.
    
    **Request Body:**
    - `urls`: List of URLs to analyze (max 100)
    - URLs must be valid HTTP/HTTPS URLs
    
    **Query Parameters:**
    - `url`: Optional filter to get results for specific URL only
    - `page`: Page number for paginated results (when job is completed)
    - `records_per_page`: Number of results per page (when job is completed)
    
    **Cache Behavior:**
    - Results are cached for 30 days by default
    - Use `ignore_cache=true` to force fresh analysis
    - Cache key is based on exact URL list
    
    **Response:**
    - Returns job details with current status
    - Use GET endpoint with job_id to check progress and get final results
    """
    domain, job_id, ignore_cache = validated_params
    
    # If job_id is provided, check if operation exists
    if job_id:
        operation_status = operations.get_operation_status(job_id)
        if not operation_status:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Job {job_id} not found")

        # If the operation is completed, return paginated results
        if operation_status["status"] == TaskStatus.COMPLETED:
            return _handle_completed_operation(job_id, url, page, records_per_page)
        
        # For running, failed, or killed operations, return standard status
        elif operation_status["status"] in [TaskStatus.FAILED, TaskStatus.KILLED]:
            operation = operations.get_operation(job_id)
        else:
            # For running operations, use the status info which includes real-time progress
            operation = operation_status

        return create_task_response(operation)

    try:
        urls = request.urls
        
        # Create parameters for cache checking
        parameters = {"urls": urls}

        # First, always check for running or pending tasks, regardless of ignore_cache
        running_operation = operations.get_running_operation("tech_detection", parameters)
        
        if running_operation:
            job_id = running_operation['_id']
            logger.info(f"Found existing running tech detection task {job_id}")
            return create_task_response(running_operation)
        
        # Then, check for existing recent results if not ignoring cache  
        if not ignore_cache:
            # Calculate cutoff time
            cutoff_time = datetime.now() - TASK_EXPIRATION.get("tech_detection", TASK_EXPIRATION["default"])
            
            recent_operation = operations.get_recent_operation(
                feature="tech_detection", 
                parameters=parameters,
                cutoff_time=cutoff_time
            )
            
            if recent_operation:
                return _handle_cached_operation(recent_operation, url, page, records_per_page)

        # No recent results found, start a new task
        logger.info(f"Starting new technology detection for {len(urls)} URLs")

        # Create operation in MongoDB
        job_id = operations.create_operation(
            feature="tech_detection",
            parameters=parameters
        )
        
        # Start the task - pass the job_id to the Celery task
        run_tech_detection.delay(urls=urls, job_id=job_id)
        
        # Return response with the job_id
        return TaskResponse(
            job_id=job_id,
            status=TaskStatus.PENDING,
            feature="tech_detection",
        )

    except Exception as e:
        logger.exception(f"Error in tech detection POST endpoint: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error starting task: {str(e)}"
        )