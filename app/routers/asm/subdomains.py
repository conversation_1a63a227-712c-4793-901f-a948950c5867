"""
Subdomain enumeration route.
"""
import logging
from typing import Op<PERSON>, <PERSON><PERSON>
from datetime import datetime

from fastapi import APIRouter, HTTPException, status, Depends

from app.db import operations
from app.models.task import TaskStatus, TaskResponse
from app.tasks.asm.subdomain_enumeration import run_subdomain_enumeration
from app.validators import validate_domain_param
from app.utils.response_utils import create_task_response
from app.config import TASK_EXPIRATION

logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/subdomains", response_model=TaskResponse)
async def get_subdomains(
    validated_params: Tuple[Optional[str], Optional[str], bool] = Depends(validate_domain_param)
):
    """Enumerate subdomains for a domain

    Set ignore_cache=true to bypass stored results and force a new scan
    """
    domain, job_id, ignore_cache = validated_params

    # If job_id is provided, check if operation exists
    if job_id:
        # Get operation status (combines database and Celery status)
        operation_status = operations.get_operation_status(job_id)
        if not operation_status:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Task {job_id} not found")

        # If the operation is completed or failed, get the full operation with results
        if operation_status["status"] in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.KILLED]:
            operation = operations.get_operation(job_id)
        else:
            # For running operations, use the status info which includes real-time progress
            operation = operation_status

        return create_task_response(operation)

    try:
        # First, always check for running or pending tasks, regardless of ignore_cache
        running_operation = operations.get_running_operation("subdomain_enumeration", {"domain": domain})

        if running_operation:
            # Extract the job_id from the MongoDB _id field
            job_id = running_operation['_id']
            logger.info(f"Found existing running task {job_id} for domain {domain}")
            return create_task_response(running_operation)

        # Then, check for completed or failed tasks if ignore_cache is not set
        if not ignore_cache:
            # Calculate cutoff time
            cutoff_time = datetime.now() - TASK_EXPIRATION.get("subdomain_enumeration", TASK_EXPIRATION["default"])

            # Find recent operation
            recent_operation = operations.get_recent_operation(
                feature="subdomain_enumeration",
                parameters={"domain": domain},
                cutoff_time=cutoff_time
            )

            if recent_operation:
                # Create response with cache_hit flag
                response = create_task_response(recent_operation)
                response.cache_hit = True
                return response

        # No recent results found, start a new task
        logger.info(f"Starting new scan for domain {domain}")

        # Create operation in MongoDB
        job_id = operations.create_operation(
            feature="subdomain_enumeration",
            parameters={"domain": domain}
        )

        # Start the task - pass the job_id to the Celery task
        run_subdomain_enumeration.delay(domain, job_id)

        # Return response with the job_id
        return TaskResponse(
            job_id=job_id,
            status=TaskStatus.PENDING,
            feature="subdomain_enumeration",
        )
    except Exception as e:
        logger.exception(f"Error starting subdomain enumeration task: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error starting task: {str(e)}",
        )
