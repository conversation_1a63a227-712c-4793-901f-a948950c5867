"""
Subdomain enumeration route.
"""
import logging
from typing import Op<PERSON>, <PERSON><PERSON>
from datetime import datetime

from fastapi import APIRouter, HTTPException, status, Query

from app.db import operations
from app.models.task import TaskStatus, TaskResponse
from app.models.subdomain_request import SubdomainRequest
from app.tasks.asm.subdomain_enumeration import run_subdomain_enumeration
from app.utils.response_utils import create_task_response
from app.config import TASK_EXPIRATION

logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/subdomains", response_model=TaskResponse)
async def post_subdomains(
    request: SubdomainRequest,
    job_id: Optional[str] = Query(None, description="Task ID from a previous request to check status")
):
    """Start subdomain enumeration for a domain

    Request Body:
    {
        "domain": "example.com"
    }
    """

    try:
        # If job_id is provided, check if operation exists
        if job_id:
            operation_status = operations.get_operation_status(job_id)
            if not operation_status:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Task {job_id} not found")
            return create_task_response(operation_status)

        # Extract domain from request
        domain = request.domain

        # Start a new task
        logger.info(f"Starting new subdomain enumeration for domain {domain}")

        # Create operation in MongoDB
        job_id = operations.create_operation(
            feature="subdomain_enumeration",
            parameters={"domain": domain}
        )

        # Start the task - pass the job_id to the Celery task
        run_subdomain_enumeration.delay(domain, job_id)

        # Return response with the job_id
        return TaskResponse(
            job_id=job_id,
            status=TaskStatus.PENDING,
            feature="subdomain_enumeration",
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error starting subdomain enumeration task: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error starting task: {str(e)}",
        )
