"""
Task Management System (TSM) job management routes.
"""
import logging
from typing import Optional, List, Dict, Any
from datetime import datetime
from math import ceil

from fastapi import APIRouter, HTTPException, status, Query
from celery.result import AsyncResult

from app.db import operations
from app.models.task import TaskStatus, TaskResponse
from app.utils.response_utils import create_task_response
from app.tasks.celery_app import celery_app

logger = logging.getLogger(__name__)

router = APIRouter()


async def _get_chunked_data(job_id: str, feature: str, page: int, records_per_page: int, stats: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    Retrieve paginated data from chunked storage based on feature type.
    """
    try:
        # Calculate which chunks we need for this page
        chunk_size = stats.get("chunk_size", 50) or stats.get("records_per_chunk", 1000) or 50
        total_chunks = stats.get("total_chunks", 0)

        if total_chunks == 0:
            return []

        # Calculate chunk range needed for this page
        start_record = (page - 1) * records_per_page
        end_record = start_record + records_per_page

        start_chunk = start_record // chunk_size
        end_chunk = min(ceil(end_record / chunk_size), total_chunks)

        # Get chunks based on feature type
        all_records = []

        for chunk_number in range(start_chunk, end_chunk):
            chunk_data = None

            if feature == "subdomain_enumeration":
                chunk_data = operations.get_subdomain_enumeration_chunk(job_id, chunk_number)
            elif feature == "webserver_discovery":
                chunk_data = operations.get_webserver_discovery_chunk(job_id, chunk_number)
            elif feature == "tech_detection":
                chunk_data = operations.get_tech_detection_chunk(job_id, chunk_number)
            elif feature == "stack_vulnerabilities":
                chunk_data = operations.get_stack_vulnerabilities_chunk(job_id, chunk_number)
            elif feature == "git_search":
                chunk_data = operations.get_git_search_chunk(job_id, chunk_number)
            elif feature == "pastes_search":
                chunk_data = operations.get_pastes_search_chunk(job_id, chunk_number)
            elif feature == "darkweb_leaks":
                chunk_data = operations.get_darkweb_leaks_chunk(job_id, chunk_number)
            elif feature == "phishing_domains":
                chunk_data = operations.get_phishing_domains_chunk(job_id, chunk_number)

            if chunk_data and chunk_data.get("data"):
                all_records.extend(chunk_data["data"])

        # Apply pagination to the combined records
        start_index_in_combined = start_record - (start_chunk * chunk_size)
        end_index_in_combined = start_index_in_combined + records_per_page

        return all_records[start_index_in_combined:end_index_in_combined]

    except Exception as e:
        logger.exception(f"Error retrieving chunked data for {job_id}, feature {feature}: {e}")
        return []


@router.get("/jobs/{job_id}", response_model=TaskResponse)
async def get_job_details(
    job_id: str,
    page: int = Query(1, description="Page number for detailed results", ge=1),
    records_per_page: int = Query(100, description="Number of records per page", ge=1, le=1000)
):
    """
    Get complete details of a scan job by job_id.
    
    Returns full scan results with pagination support for large datasets.
    """
    try:
        # Get operation status (combines database and Celery status)
        operation_status = operations.get_operation_status(job_id)
        if not operation_status:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Job {job_id} not found")

        # If the operation is completed or failed, get the full operation with results
        if operation_status["status"] in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.KILLED]:
            operation = operations.get_operation(job_id)
            if operation:
                response = create_task_response(operation)

                # Handle chunked data retrieval for completed operations
                if operation.get("structured_output") and isinstance(operation["structured_output"], dict):
                    structured_output = operation["structured_output"]
                    feature = operation.get("feature", "")

                    # Get stats from structured_output
                    stats = structured_output.get("stats", {})

                    # Try to retrieve chunked data based on feature type
                    paginated_data = None
                    total_records = 0

                    try:
                        if feature in ["public_ip_search", "nuclei_scan", "phishing_domains"]:
                            # These features store data directly in structured_output
                            data = structured_output.get("data", [])
                            total_records = len(data)
                            start_idx = (page - 1) * records_per_page
                            end_idx = start_idx + records_per_page
                            paginated_data = data[start_idx:end_idx]

                        elif feature in ["subdomain_enumeration", "webserver_discovery", "tech_detection", "stack_vulnerabilities",
                                       "git_search", "pastes_search", "darkweb_leaks"]:
                            # These features use chunked storage - all now use standardized "total" field
                            total_records = stats.get("total", 0)

                            if total_records > 0:
                                paginated_data = await _get_chunked_data(job_id, feature, page, records_per_page, stats)

                    except Exception as chunk_error:
                        logger.warning(f"Error retrieving chunked data for {job_id}: {chunk_error}")
                        # Fall back to empty results
                        paginated_data = []
                        total_records = 0

                    # Update response with paginated data
                    if paginated_data is not None:
                        response.results = {
                            **structured_output,
                            "records": paginated_data,
                            "pagination": {
                                "page": page,
                                "records_per_page": records_per_page,
                                "total_records": total_records,
                                "total_pages": ceil(total_records / records_per_page) if total_records > 0 else 1,
                                "has_next": page * records_per_page < total_records,
                                "has_prev": page > 1
                            }
                        }

                return response
        
        # For running operations, use the status info
        return create_task_response(operation_status)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error getting job details for {job_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving job details: {str(e)}"
        )


@router.get("/jobs", response_model=List[Dict[str, Any]])
async def list_jobs(
    status_filter: Optional[str] = Query(None, description="Filter by status: running, completed, failed, killed, pending"),
    feature: Optional[str] = Query(None, description="Filter by feature type"),
    limit: int = Query(50, description="Number of jobs to return", ge=1, le=500),
    offset: int = Query(0, description="Number of jobs to skip", ge=0)
):
    """
    List all jobs with filtering capabilities.
    
    Supports filtering by status, feature type, and pagination.
    """
    try:
        # Build filter criteria
        filter_criteria = {}
        
        if status_filter:
            if status_filter not in ["running", "completed", "failed", "killed", "pending"]:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid status filter. Must be one of: running, completed, failed, killed, pending"
                )
            filter_criteria["status"] = status_filter
            
        if feature:
            filter_criteria["feature"] = feature
        
        # Get jobs from database
        jobs = operations.list_operations(
            filter_criteria=filter_criteria,
            limit=limit,
            offset=offset
        )
        
        # Enhance with real-time Celery status for running jobs
        enhanced_jobs = []
        for job in jobs:
            job_data = {
                "job_id": job["_id"],
                "feature": job.get("feature"),
                "status": job.get("status"),
                "created_at": job.get("created_at"),
                "updated_at": job.get("updated_at"),
                "start_time": job.get("start_time"),
                "end_time": job.get("end_time"),
                "parameters": job.get("parameters", {})
            }
            
            # Get real-time status for running jobs
            if job.get("status") in [TaskStatus.PENDING, TaskStatus.RUNNING]:
                celery_id = job.get("task_id", job["_id"])
                celery_status = operations.get_celery_task_status(celery_id)
                if celery_status.get("status"):
                    status_mapping = {
                        "PENDING": TaskStatus.PENDING,
                        "STARTED": TaskStatus.RUNNING,
                        "SUCCESS": TaskStatus.COMPLETED,
                        "FAILURE": TaskStatus.FAILED,
                        "REVOKED": TaskStatus.KILLED
                    }
                    job_data["status"] = status_mapping.get(celery_status["status"], job_data["status"])
                    if celery_status.get("progress"):
                        job_data["progress_perc"] = celery_status["progress"]
            
            enhanced_jobs.append(job_data)
        
        return enhanced_jobs
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error listing jobs: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error listing jobs: {str(e)}"
        )


@router.delete("/jobs/{job_id}")
async def kill_job(job_id: str):
    """
    Kill/terminate a running job by job_id.
    
    This will revoke the Celery task and update the job status to 'killed'.
    """
    try:
        # Check if job exists
        operation = operations.get_operation(job_id)
        if not operation:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Job {job_id} not found")
        
        # Check if job is in a killable state
        current_status = operation.get("status")
        if current_status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.KILLED]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Cannot kill job in {current_status} status"
            )
        
        # Get Celery task ID (use stored task_id if available, otherwise use job_id)
        celery_task_id = operation.get("task_id", job_id)
        
        # Revoke the Celery task
        try:
            celery_app.control.revoke(celery_task_id, terminate=True)
            logger.info(f"Revoked Celery task {celery_task_id} for job {job_id}")
        except Exception as celery_error:
            logger.warning(f"Failed to revoke Celery task {celery_task_id}: {celery_error}")
        
        # Update job status in database
        operations.update_operation(
            job_id=job_id,
            status=TaskStatus.KILLED,
            end_time=datetime.now(),
            error="Job killed by user request"
        )
        
        return {
            "message": f"Job {job_id} has been killed",
            "job_id": job_id,
            "status": "killed"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error killing job {job_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error killing job: {str(e)}"
        )
