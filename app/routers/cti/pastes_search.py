"""
Pastes search routes using Google Custom Search Engine.
"""
import logging
from typing import Op<PERSON>, Tuple, List, Dict, Any
from datetime import datetime
from math import ceil

from fastapi import APIRouter, HTTPException, status, Query, Depends

from app.db import operations
from app.models.task import TaskStatus, ExecutionResponse
from app.models.pastes_search_request import PastesSearchRequest
from app.tasks.cti.pastes_search import run_pastes_search_by_domain
from app.validators import validate_domain_param
from app.utils.response_utils import create_task_response


logger = logging.getLogger(__name__)

router = APIRouter(prefix="/pastes_search")


@router.post("", response_model=ExecutionResponse)
async def pastes_search(request: PastesSearchRequest) -> ExecutionResponse:
    """
    Start pastes search for a domain.

    Request Body:
    {
        "domain": "example.com",
        "secrets": false,
        "cse_page_limit": 10
    }
    """
    try:
        # Extract parameters from request
        domain = request.domain
        secrets = request.secrets
        cse_page_limit = request.cse_page_limit

        # Start a new task
        logger.info(f"Starting new pastes search for domain {domain}")

        # Create operation in MongoDB
        job_id = operations.create_operation(
            feature="pastes_search",
            parameters={"domain": domain, "secrets": secrets, "cse_page_limit": cse_page_limit}
        )

        # Start the task - pass the job_id to the Celery task
        task = run_pastes_search_by_domain.delay(domain, job_id, secrets, chunk_size=100, cse_page_limit=cse_page_limit)
        logger.info(f"Celery task started with task_id {task.id}")

        # Return lightweight execution response
        return ExecutionResponse(
            job_id=job_id,
            status=TaskStatus.PENDING,
            feature="pastes_search",
            created_at=datetime.now()
        )
    except Exception as e:
        logger.exception(f"Error starting pastes search task: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error starting task: {str(e)}"
        )


    """
    Internal function to get detailed paginated results for a completed task.
    """
    try:
        # Get summary from operation
        structured_output = operation_status.get("structured_output", {})
        summary = structured_output.get("summary", {})

        if not summary:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Task {job_id} does not have summary data"
            )

        # Calculate pagination
        total_records = summary.get("total_records", 0)
        chunk_size = summary.get("chunk_size", 100)
        total_chunks = summary.get("total_chunks", 0)

        # Calculate which chunks we need for this page
        records_per_chunk = chunk_size
        start_record = (page - 1) * records_per_page
        end_record = start_record + records_per_page

        start_chunk = start_record // records_per_chunk
        end_chunk = min(ceil(end_record / records_per_chunk), total_chunks)

        # Get chunks
        chunks_to_fetch = list(range(start_chunk, end_chunk))
        records = []

        for chunk_number in chunks_to_fetch:
            chunk = operations.get_pastes_search_chunk(job_id, chunk_number)
            if chunk and "data" in chunk:
                # Calculate slice for first and last chunks
                if chunk_number == start_chunk:
                    start_idx = start_record % records_per_chunk
                else:
                    start_idx = 0

                if chunk_number == end_chunk - 1:
                    end_idx = min(end_record % records_per_chunk or records_per_chunk, len(chunk["data"]))
                else:
                    end_idx = len(chunk["data"])

                records.extend(chunk["data"][start_idx:end_idx])

                # Stop if we have enough records
                if len(records) >= records_per_page:
                    records = records[:records_per_page]
                    break

        # Calculate total pages
        total_pages = ceil(total_records / records_per_page) if total_records > 0 else 1

        # Create standard TaskResponse with results populated
        response = create_task_response(operation_status)
        
        # Populate the results field with detailed data
        if not response.results:
            response.results = {}
            
        response.results.update({
            "summary": summary,
            "records": records,
            "pagination": {
                "total_records": total_records,
                "page": page,
                "total_pages": total_pages,
                "records_per_page": records_per_page
            }
        })
        
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error getting detailed results: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving detailed results: {str(e)}"
        ) 