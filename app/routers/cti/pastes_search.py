"""
Pastes search routes using Google Custom Search Engine.
"""
import logging
from typing import Op<PERSON>, Tuple, List, Dict, Any
from datetime import datetime
from math import ceil

from fastapi import APIRouter, HTTPException, status, Query, Depends

from app.db import operations
from app.models.task import TaskStatus, TaskResponse
from app.tasks.cti.pastes_search import run_pastes_search_by_domain
from app.validators import validate_domain_param
from app.utils.response_utils import create_task_response
from app.config import TASK_EXPIRATION

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/pastes_search")


@router.get("", response_model=TaskResponse)
async def pastes_search(
    domain: str = Query(..., description="Domain to search for in pastes"),
    job_id: Optional[str] = Query(None, description="Task ID from a previous request"),
    ignore_cache: bool = Query(False, description="Force a new scan, bypassing stored results"),
    secrets: bool = Query(False, description="Analyze paste contents for secrets and sensitive information"),
    cse_page_limit: int = Query(10, description="Maximum number of CSE pages to fetch (1-100)", ge=1, le=100),
    page: int = Query(1, description="Page number for detailed results", ge=1),
    records_per_page: int = Query(100, description="Number of records per page for detailed results", ge=10, le=500)
):
    """
    Search for pastes containing the specified domain using Google Custom Search Engine.

    This unified endpoint handles all pastes search operations:
    - Start new searches
    - Check status of existing searches  
    - Return detailed paginated results for completed searches

    Parameters:
        domain: Domain to search for in pastes
        job_id: Task ID from a previous request to check status or get results
        ignore_cache: Force a new scan, bypassing stored results
        secrets: If true, fetch paste content and analyze for secrets/sensitive information
        cse_page_limit: Maximum number of CSE pages to fetch (1-100, default: 10)
        page: Page number for detailed results (when task is completed)
        records_per_page: Number of records per page for detailed results (when task is completed)
    """
    try:
        # If job_id is provided, check if operation exists
        if job_id:
            # Get operation status (combines database and Celery status)
            operation_status = operations.get_operation_status(job_id)
            if not operation_status:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Task {job_id} not found"
                )

            # Check if operation is for pastes_search
            if operation_status.get("feature") != "pastes_search":
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Task {job_id} is not a pastes search"
                )

            # If task is completed, return paginated detailed results
            if operation_status.get("status") == TaskStatus.COMPLETED:
                return await _get_detailed_results(job_id, operation_status, page, records_per_page)
            
            # Otherwise return basic status/summary
            return create_task_response(operation_status)

        # No job_id provided - start new search or return existing
        # Check for running operations with the same parameters
        running_operation = operations.get_running_operation(
            feature="pastes_search",
            parameters={"domain": domain, "secrets": secrets, "cse_page_limit": cse_page_limit}
        )

        if running_operation:
            return create_task_response(running_operation)

        # Check for recent completed operations
        if not ignore_cache:
            cutoff_time = datetime.now() - TASK_EXPIRATION.get("pastes_search", TASK_EXPIRATION["default"])
            recent_operation = operations.get_recent_operation(
                feature="pastes_search",
                parameters={"domain": domain, "secrets": secrets, "cse_page_limit": cse_page_limit},
                cutoff_time=cutoff_time
            )

            if recent_operation:
                # If task is completed, return paginated detailed results
                if recent_operation.get("status") == TaskStatus.COMPLETED:
                    return await _get_detailed_results(recent_operation["_id"], recent_operation, page, records_per_page)
                
                response = create_task_response(recent_operation)
                response.cache_hit = True
                return response

        # Start new task
        job_id = operations.create_operation(
            feature="pastes_search",
            parameters={"domain": domain, "secrets": secrets, "cse_page_limit": cse_page_limit}
        )

        # Start the Celery task
        logger.info(f"Starting Celery task for domain {domain} with secrets={secrets}, cse_page_limit={cse_page_limit} and job_id {job_id}")
        task = run_pastes_search_by_domain.delay(domain, job_id, secrets, chunk_size=100, cse_page_limit=cse_page_limit)
        logger.info(f"Celery task started with task_id {task.id}")

        return TaskResponse(
            job_id=job_id,
            status=TaskStatus.PENDING,
            feature="pastes_search",
        )
    except Exception as e:
        logger.exception(f"Error in pastes search: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error in pastes search: {str(e)}"
        )


async def _get_detailed_results(job_id: str, operation_status: Dict[str, Any], page: int, records_per_page: int):
    """
    Internal function to get detailed paginated results for a completed task.
    """
    try:
        # Get summary from operation
        structured_output = operation_status.get("structured_output", {})
        summary = structured_output.get("summary", {})

        if not summary:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Task {job_id} does not have summary data"
            )

        # Calculate pagination
        total_records = summary.get("total_records", 0)
        chunk_size = summary.get("chunk_size", 100)
        total_chunks = summary.get("total_chunks", 0)

        # Calculate which chunks we need for this page
        records_per_chunk = chunk_size
        start_record = (page - 1) * records_per_page
        end_record = start_record + records_per_page

        start_chunk = start_record // records_per_chunk
        end_chunk = min(ceil(end_record / records_per_chunk), total_chunks)

        # Get chunks
        chunks_to_fetch = list(range(start_chunk, end_chunk))
        records = []

        for chunk_number in chunks_to_fetch:
            chunk = operations.get_pastes_search_chunk(job_id, chunk_number)
            if chunk and "data" in chunk:
                # Calculate slice for first and last chunks
                if chunk_number == start_chunk:
                    start_idx = start_record % records_per_chunk
                else:
                    start_idx = 0

                if chunk_number == end_chunk - 1:
                    end_idx = min(end_record % records_per_chunk or records_per_chunk, len(chunk["data"]))
                else:
                    end_idx = len(chunk["data"])

                records.extend(chunk["data"][start_idx:end_idx])

                # Stop if we have enough records
                if len(records) >= records_per_page:
                    records = records[:records_per_page]
                    break

        # Calculate total pages
        total_pages = ceil(total_records / records_per_page) if total_records > 0 else 1

        # Create standard TaskResponse with results populated
        response = create_task_response(operation_status)
        
        # Populate the results field with detailed data
        if not response.results:
            response.results = {}
            
        response.results.update({
            "summary": summary,
            "records": records,
            "pagination": {
                "total_records": total_records,
                "page": page,
                "total_pages": total_pages,
                "records_per_page": records_per_page
            }
        })
        
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error getting detailed results: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving detailed results: {str(e)}"
        ) 