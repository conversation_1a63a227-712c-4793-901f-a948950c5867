"""
Phishing domains detection routes.
"""
import logging
from typing import <PERSON><PERSON>, <PERSON><PERSON>
from datetime import datetime

from fastapi import APIRouter, HTTPException, status, Query, Depends

from app.db import operations
from app.models.task import TaskStatus, TaskResponse
from app.tasks.cti.phishing_domains import run_phishing_domain_scan
from app.validators import validate_domain_param
from app.utils.response_utils import create_task_response
from app.config import TASK_EXPIRATION

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/phishing_domains")

@router.get("", response_model=TaskResponse)
async def scan_domain_for_phishing(
    validated_params: Tuple[Optional[str], Optional[str], bool] = Depends(validate_domain_param)
):
    """
    Scan for phishing domains based on a target domain using permutation techniques.

    This endpoint uses domain permutation algorithms to generate potential phishing domains
    and checks their registration status, DNS records, and other threat indicators.

    Parameters:
        domain: Target domain to scan for phishing variants
        job_id: Task ID from a previous request  
        ignore_cache: Force a new scan, bypassing stored results
    """
    domain, job_id, ignore_cache = validated_params

    try:
        # If job_id is provided, check if operation exists
        if job_id:
            # Get operation status (combines database and Celery status)
            operation_status = operations.get_operation_status(job_id)
            if not operation_status:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Task {job_id} not found"
                )

            # Check if operation is for the same domain
            if operation_status.get("parameters", {}).get("domain") != domain:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Task {job_id} is for a different domain"
                )

            return create_task_response(operation_status)

        # Check for running operations with the same parameters
        running_operation = operations.get_running_operation(
            feature="phishing_domains",
            parameters={"domain": domain}
        )

        if running_operation:
            return create_task_response(running_operation)

        # Check for recent completed operations
        if not ignore_cache:
            cutoff_time = datetime.now() - TASK_EXPIRATION.get("phishing_domains", TASK_EXPIRATION["default"])
            recent_operation = operations.get_recent_operation(
                feature="phishing_domains", 
                parameters={"domain": domain},
                cutoff_time=cutoff_time
            )

            if recent_operation:
                response = create_task_response(recent_operation)
                response.cache_hit = True
                return response

        # Start new task
        job_id = operations.create_operation(
            feature="phishing_domains",
            parameters={"domain": domain}
        )

        # Start the Celery task
        logger.info(f"Starting phishing domain scan for domain {domain} with job_id {job_id}")
        task = run_phishing_domain_scan.delay(domain, job_id)
        logger.info(f"Celery task started with task_id {task.id}")

        return TaskResponse(
            job_id=job_id,
            status=TaskStatus.PENDING,
            feature="phishing_domains",
        )
    except Exception as e:
        logger.exception(f"Error in phishing domains scan: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error starting task: {str(e)}"
        ) 