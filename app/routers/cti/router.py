"""
Main router for Cyber Threat Intelligence (CTI) routes.
"""
from fastapi import APIRouter


from app.routers.cti.phishing_domains import router as phishing_domains_router
from app.routers.cti.pastes_search import router as pastes_search_router
from app.routers.cti.git_search import router as git_search_router
from app.routers.cti.darkweb_leaks import router as darkweb_leaks_router

# Create main CTI router
router = APIRouter()

# Include all CTI routes

router.include_router(phishing_domains_router)
router.include_router(pastes_search_router)
router.include_router(git_search_router)
router.include_router(darkweb_leaks_router)
