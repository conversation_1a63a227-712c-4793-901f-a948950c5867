"""
Git search routes using git-hound tool.
"""
import logging
import json
from typing import Optional, List, Dict, Any
from datetime import datetime
from math import ceil

from fastapi import APIRouter, HTTPException, status, Query, Depends

from app.db import operations
from app.models.task import TaskStatus, ExecutionResponse
from app.models.git_search_request import GitSearchRequest
from app.tasks.cti.git_search import run_git_search_by_identifiers
from app.utils.response_utils import create_task_response
from app.config import TASK_EXPIRATION

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/git_search")


@router.post("", response_model=ExecutionResponse)
async def git_search(request: GitSearchRequest) -> ExecutionResponse:
    """
    Start GitHub search for leaks containing the specified identifiers.

    Request Body:
    {
        "identifiers": ["example.com", "<EMAIL>"],
        "git_depth": 10
    }
    """
    try:
        # Extract identifiers and git_depth from request
        identifiers_list = request.identifiers
        git_depth = request.git_depth

        # Start a new task
        logger.info(f"Starting new git search for identifiers {identifiers_list}")

        # Create operation in MongoDB
        job_id = operations.create_operation(
            feature="git_search",
            parameters={"identifiers": identifiers_list, "git_depth": git_depth}
        )

        # Start the task - pass the job_id to the Celery task
        task = run_git_search_by_identifiers.delay(identifiers_list, job_id, chunk_size=100, git_depth=git_depth)
        logger.info(f"Celery task started with task_id {task.id}")

        # Return lightweight execution response
        return ExecutionResponse(
            job_id=job_id,
            status=TaskStatus.PENDING,
            feature="git_search",
            created_at=datetime.now()
        )
    except Exception as e:
        logger.exception(f"Error starting git search task: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error starting task: {str(e)}",
        )

    """
    Internal function to get detailed paginated results for a completed task.
    """
    try:
        # Get summary from operation
        structured_output = operation_status.get("structured_output", {})
        summary = structured_output.get("summary", {})

        if not summary:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Task {job_id} does not have summary data"
            )

        # Calculate pagination
        total_records = summary.get("total_records", 0)
        chunk_size = summary.get("chunk_size", 100)
        total_chunks = summary.get("total_chunks", 0)

        # Calculate which chunks we need for this page
        records_per_chunk = chunk_size
        start_record = (page - 1) * records_per_page
        end_record = start_record + records_per_page

        start_chunk = start_record // records_per_chunk
        end_chunk = min(ceil(end_record / records_per_chunk), total_chunks)

        # Get chunks
        chunks_to_fetch = list(range(start_chunk, end_chunk))
        records = []

        for chunk_number in chunks_to_fetch:
            chunk = operations.get_git_search_chunk(job_id, chunk_number)
            if chunk and "data" in chunk:
                # Calculate slice for first and last chunks
                if chunk_number == start_chunk:
                    start_idx = start_record % records_per_chunk
                else:
                    start_idx = 0

                if chunk_number == end_chunk - 1:
                    end_idx = min(end_record % records_per_chunk or records_per_chunk, len(chunk["data"]))
                else:
                    end_idx = len(chunk["data"])

                records.extend(chunk["data"][start_idx:end_idx])

                # Stop if we have enough records
                if len(records) >= records_per_page:
                    records = records[:records_per_page]
                    break

        # Calculate total pages
        total_pages = ceil(total_records / records_per_page) if total_records > 0 else 1

        # Create standard TaskResponse with results populated
        response = create_task_response(operation_status)
        
        # Populate the results field with detailed data
        if not response.results:
            response.results = {}
            
        response.results.update({
            "summary": summary,
            "records": records,
            "pagination": {
                "total_records": total_records,
                "page": page,
                "total_pages": total_pages,
                "records_per_page": records_per_page
            }
        })
        
        return response

    except Exception as e:
        logger.exception(f"Error getting detailed results for job {job_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting detailed results: {str(e)}"
        ) 