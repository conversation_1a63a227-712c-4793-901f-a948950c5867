"""
Operations database module for ThreatMesh API.

This module provides a unified interface for working with operations
(combined tasks and results) in the database.
"""
import os
import uuid
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple

from pymongo import MongoClient
from pymongo.collection import Collection
from celery.result import AsyncResult
from dotenv import load_dotenv

from app.models.task import TaskStatus

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)

# MongoDB connection settings
MONGODB_URI = os.getenv("MONGODB_URI", "mongodb://localhost:27017")
MONGODB_DB = os.getenv("MONGODB_DB", "threatmesh")

# MongoDB client
client = MongoClient(MONGODB_URI)
db = client[MONGODB_DB]

# Operations collection (replaces tasks and results)
operations_collection = db["operations"]


# Collection for storing detailed pastes search results
pastes_search_collection = db["pastes_search"]

# Collection for storing detailed git search results
git_search_collection = db["git_search"]

# Collection for storing detailed darkweb leaks results
darkweb_leaks_collection = db["darkweb_leaks"]

# Collection for storing detailed webserver discovery results
webserver_discovery_collection = db["webserver_discovery"]

# Collection for storing detailed tech detection results
tech_detection_collection = db["tech_detection"]

# Collection for storing detailed stack vulnerabilities results
stack_vulnerabilities_collection = db["stack_vulnerabilities"]


def init_db():
    """Initialize database with indexes"""
    try:
        # Create indexes for operations collection
        operations_collection.create_index("feature")
        operations_collection.create_index("status")
        operations_collection.create_index([("parameters.domain", 1)])
        operations_collection.create_index([("parameters.keyword", 1)])
        
        
        # Create indexes for pastes search collection
        pastes_search_collection.create_index("operation_id")
        pastes_search_collection.create_index("chunk_number")
        pastes_search_collection.create_index("domain")
        
        # Create indexes for git search collection
        git_search_collection.create_index("operation_id")
        git_search_collection.create_index("chunk_number")
        git_search_collection.create_index("repo")
        git_search_collection.create_index("rule_id")
        
        # Create indexes for darkweb leaks collection
        darkweb_leaks_collection.create_index("operation_id")
        darkweb_leaks_collection.create_index("chunk_number")
        darkweb_leaks_collection.create_index("domain")
        darkweb_leaks_collection.create_index("database_name")

        # Create indexes for webserver discovery collection
        webserver_discovery_collection.create_index("operation_id")
        webserver_discovery_collection.create_index("chunk_number")
        webserver_discovery_collection.create_index("domain")

# Create indexes for tech detection collection
        tech_detection_collection.create_index("operation_id")
        tech_detection_collection.create_index("chunk_number")

        # Create indexes for stack vulnerabilities collection
        stack_vulnerabilities_collection.create_index("operation_id")
        stack_vulnerabilities_collection.create_index("chunk_number")

        logger.info("Database initialized successfully")
    except Exception as e:
        logger.error(f"Error initializing database: {e}")
        raise


def create_operation(feature: str, parameters: Dict[str, Any], job_id: str = None) -> str:
    """
    Create a new operation.

    Args:
        feature: Feature name (e.g., 'subdomain_enumeration')
        parameters: Operation parameters (e.g., {'domain': 'example.com'})
        job_id: Optional task ID (generated if not provided)

    Returns:
        str: Operation ID
    """
    if not job_id:
        job_id = str(uuid.uuid4())

    document = {
        "_id": job_id,
        "feature": feature,
        "parameters": parameters,
        "status": TaskStatus.PENDING,
        "created_at": datetime.now(),
        "updated_at": datetime.now()
    }

    operations_collection.insert_one(document)
    logger.info(f"Created operation {job_id} for feature {feature}")
    return job_id


def get_operation(job_id: str) -> Optional[Dict[str, Any]]:
    """
    Get operation by ID.

    Args:
        job_id: Operation ID

    Returns:
        Optional[Dict[str, Any]]: Operation document or None if not found
    """
    return operations_collection.find_one({"_id": job_id})


def get_celery_task_status(job_id: str) -> Dict[str, Any]:
    """
    Get the status of a Celery task.

    Args:
        job_id: Celery task ID

    Returns:
        Dict[str, Any]: Task status information
    """
    try:
        result = AsyncResult(job_id)
        status_info = {
            "status": result.state,
            "ready": result.ready(),
            "successful": result.successful() if result.ready() else None
        }

        # Extract progress information if available
        if result.info and isinstance(result.info, dict):
            status_info["progress"] = result.info.get("progress")
            status_info["current"] = result.info.get("current")
            status_info["total"] = result.info.get("total")
            status_info["message"] = result.info.get("message")

        return status_info
    except Exception as e:
        logger.warning(f"Error getting Celery task status for {job_id}: {e}")
        return {"status": "UNKNOWN", "error": str(e)}


def get_operation_status(job_id: str) -> Optional[Dict[str, Any]]:
    """
    Get operation status, combining database and Celery information.

    Args:
        job_id: Operation ID

    Returns:
        Optional[Dict[str, Any]]: Operation status or None if not found
    """
    # First check the database
    operation = operations_collection.find_one(
        {"_id": job_id},
        {"status": 1, "feature": 1, "parameters": 1, "start_time": 1,
         "end_time": 1, "error": 1, "_id": 1, "progress_perc": 1, "task_id": 1, "structured_output": 1}
    )

    if not operation:
        return None

    # If the task is completed or failed, return database info
    if operation.get("status") in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.KILLED]:
        return operation

    # Otherwise, get real-time status from Celery
    # Use the stored Celery task ID if available, otherwise use our task ID
    celery_id = operation.get("task_id", job_id)
    celery_status = get_celery_task_status(celery_id)

    # Map Celery status to our status enum
    status_mapping = {
        "PENDING": TaskStatus.PENDING,
        "STARTED": TaskStatus.RUNNING,
        "SUCCESS": TaskStatus.COMPLETED,
        "FAILURE": TaskStatus.FAILED,
        "REVOKED": TaskStatus.KILLED
    }

    # Update operation with Celery status
    celery_task_status = celery_status.get("status", "UNKNOWN")
    operation["status"] = status_mapping.get(celery_task_status, operation.get("status", TaskStatus.PENDING))

    # Add progress if available
    if celery_status.get("progress") is not None:
        operation["progress_perc"] = celery_status["progress"]

    # Add message if available
    if celery_status.get("message"):
        operation["message"] = celery_status["message"]

    return operation


def update_operation(
    job_id: str,
    status: TaskStatus = None,
    structured_output: Dict[str, Any] = None,
    raw_output: List[str] = None,
    error: str = None,
    start_time: datetime = None,
    end_time: datetime = None,
    progress_perc: int = None,
    task_id: str = None
) -> bool:
    """
    Update operation with new information.

    Args:
        job_id: Operation ID
        status: New status
        structured_output: Structured output data
        raw_output: Raw output lines
        error: Error message
        start_time: Start time
        end_time: End time
        progress_perc: Progress percentage
        task_id: Celery task ID for status queries

    Returns:
        bool: True if operation was updated
    """
    update = {"$set": {"updated_at": datetime.now()}}

    if status is not None:
        update["$set"]["status"] = status

    if structured_output is not None:
        update["$set"]["structured_output"] = structured_output

    if raw_output is not None:
        update["$set"]["raw_output"] = raw_output

    if error is not None:
        update["$set"]["error"] = error

    if start_time is not None:
        update["$set"]["start_time"] = start_time

    if end_time is not None:
        update["$set"]["end_time"] = end_time

    if progress_perc is not None:
        update["$set"]["progress_perc"] = progress_perc

    if task_id is not None:
        update["$set"]["task_id"] = task_id

    result = operations_collection.update_one({"_id": job_id}, update)
    return result.modified_count > 0


def add_raw_output(job_id: str, output: str) -> bool:
    """
    Add raw output line to operation.

    Args:
        job_id: Operation ID
        output: Output line

    Returns:
        bool: True if operation was updated
    """
    update = {
        "$push": {"raw_output": output},
        "$set": {"updated_at": datetime.now()}
    }

    result = operations_collection.update_one({"_id": job_id}, update)
    return result.modified_count > 0


def get_running_operation(feature: str, parameters: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Get running operation by feature and parameters.

    Args:
        feature: Feature name
        parameters: Operation parameters

    Returns:
        Optional[Dict[str, Any]]: Operation document or None if not found
    """
    query = {
        "feature": feature,
        "status": {"$in": [TaskStatus.PENDING, TaskStatus.RUNNING]}
    }

    # Add parameters to query
    for key, value in parameters.items():
        query[f"parameters.{key}"] = value

    operation = operations_collection.find_one(query)

    if operation:
        # Get real-time status from Celery
        operation_status = get_operation_status(operation["_id"])
        return operation_status

    return None




def store_pastes_search_chunk(operation_id: str, domain: str, chunk_number: int, data: List[Dict[str, Any]]) -> str:
    """
    Store a chunk of pastes search results in the database.

    Args:
        operation_id: Operation ID
        domain: Domain being searched
        chunk_number: Chunk number for pagination
        data: List of paste records

    Returns:
        str: ID of the inserted document
    """
    document = {
        "operation_id": operation_id,
        "domain": domain,
        "chunk_number": chunk_number,
        "created_at": datetime.now(),
        "count": len(data),
        "data": data
    }

    result = pastes_search_collection.insert_one(document)
    return str(result.inserted_id)


def get_pastes_search_chunk(operation_id: str, chunk_number: int) -> Optional[Dict[str, Any]]:
    """
    Get a specific chunk of pastes search results.

    Args:
        operation_id: Operation ID
        chunk_number: Chunk number

    Returns:
        Optional[Dict[str, Any]]: Chunk document or None if not found
    """
    return pastes_search_collection.find_one({
        "operation_id": operation_id,
        "chunk_number": chunk_number
    })


def save_git_search_chunk(operation_id: str, chunk_number: int, data: List[Dict[str, Any]]) -> str:
    """
    Store a chunk of git search results in the database.

    Args:
        operation_id: Operation ID
        chunk_number: Chunk number for pagination
        data: List of git search records

    Returns:
        str: ID of the inserted document
    """
    document = {
        "operation_id": operation_id,
        "chunk_number": chunk_number,
        "created_at": datetime.now(),
        "count": len(data),
        "data": data
    }

    result = git_search_collection.insert_one(document)
    return str(result.inserted_id)


def get_git_search_chunk(operation_id: str, chunk_number: int) -> Optional[Dict[str, Any]]:
    """
    Get a specific chunk of git search results.

    Args:
        operation_id: Operation ID
        chunk_number: Chunk number

    Returns:
        Optional[Dict[str, Any]]: Chunk document or None if not found
    """
    return git_search_collection.find_one({
        "operation_id": operation_id,
        "chunk_number": chunk_number
    })


def store_darkweb_leaks_chunk(operation_id: str, domain: str, chunk_number: int, data: List[Dict[str, Any]]) -> str:
    """
    Store a chunk of darkweb leaks results in the database.

    Args:
        operation_id: Operation ID
        domain: Domain being searched
        chunk_number: Chunk number for pagination
        data: List of darkweb leak records

    Returns:
        str: ID of the inserted document
    """
    document = {
        "operation_id": operation_id,
        "domain": domain,
        "chunk_number": chunk_number,
        "created_at": datetime.now(),
        "count": len(data),
        "data": data
    }

    result = darkweb_leaks_collection.insert_one(document)
    return str(result.inserted_id)


def get_darkweb_leaks_chunk(operation_id: str, chunk_number: int) -> Optional[Dict[str, Any]]:
    """
    Get a specific chunk of darkweb leaks results.

    Args:
        operation_id: Operation ID
        chunk_number: Chunk number

    Returns:
        Optional[Dict[str, Any]]: Chunk document or None if not found
    """
    return darkweb_leaks_collection.find_one({
        "operation_id": operation_id,
        "chunk_number": chunk_number
    })


def store_webserver_discovery_chunk(operation_id: str, domain: str, chunk_number: int, data: List[Dict[str, Any]]) -> str:
    """
    Store a chunk of webserver discovery results in the database.

    Args:
        operation_id: Operation ID
        domain: Domain being scanned
        chunk_number: Chunk number for pagination
        data: List of webserver records

    Returns:
        str: ID of the inserted document
    """
    document = {
        "operation_id": operation_id,
        "domain": domain,
        "chunk_number": chunk_number,
        "created_at": datetime.now(),
        "count": len(data),
        "data": data
    }

    result = webserver_discovery_collection.insert_one(document)
    return str(result.inserted_id)


def get_webserver_discovery_chunk(operation_id: str, chunk_number: int) -> Optional[Dict[str, Any]]:
    """
    Get a specific chunk of webserver discovery results.

    Args:
        operation_id: Operation ID
        chunk_number: Chunk number

    Returns:
        Optional[Dict[str, Any]]: Chunk document or None if not found
    """
    return webserver_discovery_collection.find_one({
        "operation_id": operation_id,
        "chunk_number": chunk_number
    })


def store_tech_detection_chunk(operation_id: str, chunk_number: int, data: List[Dict[str, Any]]) -> str:
    """
    Store a chunk of tech detection results in the database.

    Args:
        operation_id: Operation ID
        chunk_number: Chunk number for pagination
        data: List of tech detection records

    Returns:
        str: ID of the inserted document
    """
    document = {
        "operation_id": operation_id,
        "chunk_number": chunk_number,
        "created_at": datetime.now(),
        "count": len(data),
        "data": data
    }

    result = tech_detection_collection.insert_one(document)
    return str(result.inserted_id)


def get_tech_detection_chunk(operation_id: str, chunk_number: int) -> Optional[Dict[str, Any]]:
    """
    Get a specific chunk of tech detection results.

    Args:
        operation_id: Operation ID
        chunk_number: Chunk number

    Returns:
        Optional[Dict[str, Any]]: Chunk document or None if not found
    """
    return tech_detection_collection.find_one({
        "operation_id": operation_id,
        "chunk_number": chunk_number
    })


def store_stack_vulnerabilities_chunk(operation_id: str, chunk_number: int, data: List[Dict[str, Any]]) -> str:
    """
    Store a chunk of stack vulnerabilities results in the database.

    Args:
        operation_id: Operation ID
        chunk_number: Chunk number for pagination
        data: List of stack vulnerability records

    Returns:
        str: ID of the inserted document
    """
    document = {
        "operation_id": operation_id,
        "chunk_number": chunk_number,
        "created_at": datetime.now(),
        "count": len(data),
        "data": data
    }

    result = stack_vulnerabilities_collection.insert_one(document)
    return str(result.inserted_id)


def get_stack_vulnerabilities_chunk(operation_id: str, chunk_number: int) -> Optional[Dict[str, Any]]:
    """
    Get a specific chunk of stack vulnerabilities results.

    Args:
        operation_id: Operation ID
        chunk_number: Chunk number

    Returns:
        Optional[Dict[str, Any]]: Chunk document or None if not found
    """
    return stack_vulnerabilities_collection.find_one({
        "operation_id": operation_id,
        "chunk_number": chunk_number
    })


def get_webserver_urls_by_domain(domain: str) -> List[str]:
    """
    Get all webserver URLs for a specific domain from the most recent webserver discovery operation.

    Args:
        domain: Domain to get webserver URLs for

    Returns:
        List[str]: List of URLs or empty list if none found
    """
    try:
        # Find the most recent completed webserver discovery operation for this domain
        operation = operations_collection.find_one(
            {
                "feature": "webserver_discovery",
                "parameters.domain": domain,
                "status": TaskStatus.COMPLETED
            },
            sort=[("end_time", -1)]
        )
        
        if not operation:
            logger.info(f"No completed webserver discovery operation found for domain {domain}")
            return []
        
        operation_id = operation["_id"]
        
        # Find all chunks for this operation
        chunks = webserver_discovery_collection.find({"operation_id": operation_id})
        
        urls = []
        for chunk in chunks:
            webserver_data = chunk.get("data", [])
            for webserver in webserver_data:
                if webserver.get("url"):
                    urls.append(webserver["url"])
        
        return urls
        
    except Exception as e:
        logger.error(f"Error retrieving webserver URLs for domain {domain}: {e}")
        return []


def get_tech_detection_results_by_url(operation_id: str, url: str) -> List[Dict[str, Any]]:
    """
    Get all tech detection results for a specific URL from a tech detection operation.

    Args:
        operation_id: Operation ID to search within
        url: URL to get tech detection results for

    Returns:
        List[Dict[str, Any]]: List of tech detection results or empty list if none found
    """
    try:
        # Find all chunks for this operation and filter by URL
        chunks = tech_detection_collection.find({"operation_id": operation_id})
        
        technologies = []
        for chunk in chunks:
            tech_data = chunk.get("data", [])
            # Filter results by URL
            for tech_record in tech_data:
                if tech_record.get("url") == url:
                    technologies.append(tech_record)
        
        return technologies
        
    except Exception as e:
        logger.error(f"Error retrieving tech detection results for URL {url} from operation {operation_id}: {e}")
        return []


def get_all_tech_detection_results_by_domain(domain: str) -> List[Dict[str, Any]]:
    """
    Get all tech detection results for a domain from the most recent tech detection operation.

    Args:
        domain: Domain to get tech detection results for

    Returns:
        List[Dict[str, Any]]: List of all tech detection results or empty list if none found
    """
    try:
        # Find the most recent completed tech detection operation for this domain
        operation = operations_collection.find_one(
            {
                "feature": "tech_detection",
                "parameters.domain": domain,
                "status": TaskStatus.COMPLETED
            },
            sort=[("end_time", -1)]
        )
        
        if not operation:
            logger.info(f"No completed tech detection operation found for domain {domain}")
            return []
        
        operation_id = operation["_id"]
        
        # Find all chunks for this operation
        chunks = tech_detection_collection.find({"operation_id": operation_id})
        
        all_results = []
        for chunk in chunks:
            tech_data = chunk.get("data", [])
            all_results.extend(tech_data)
        
        return all_results
        
    except Exception as e:
        logger.error(f"Error retrieving tech detection results for domain {domain}: {e}")
        return []


def list_operations(filter_criteria: Dict[str, Any] = None, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
    """
    List operations with filtering and pagination.

    Args:
        filter_criteria: Dictionary of filter criteria
        limit: Maximum number of operations to return
        offset: Number of operations to skip

    Returns:
        List[Dict[str, Any]]: List of operations
    """
    try:
        query = filter_criteria or {}

        cursor = operations_collection.find(query).sort("created_at", -1).skip(offset).limit(limit)
        operations = list(cursor)

        logger.info(f"Retrieved {len(operations)} operations with filter {query}")
        return operations
    except Exception as e:
        logger.exception(f"Error listing operations: {e}")
        return []
