version: "3.9"

services:
  redis:
    image: redis:7.2-alpine
    container_name: threatmesh-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - threatmesh-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    command: redis-server --appendonly yes

  api:
    image: ghcr.io/curlsek/threatmesh:latest
    container_name: threatmesh-api
    restart: unless-stopped
    ports:
      - "8000:8000"
    env_file:
      - .env
    networks:
      - threatmesh-network
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 3
    command: ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
    logging:
      driver: awslogs
      options:
        awslogs-region: ap-south-1
        awslogs-group: /docker/threatmesh
        awslogs-stream: threatmesh

  worker:
    image: ghcr.io/curlsek/threatmesh:latest
    container_name: threatmesh-worker
    restart: unless-stopped
    env_file:
      - .env
    environment:
      - DOCKER_HOST=unix:///var/run/docker.sock
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - /usr/bin/docker:/usr/bin/docker
    networks:
      - threatmesh-network
    depends_on:
      redis:
        condition: service_healthy
      api:
        condition: service_started
    command: ["python", "-m", "celery", "-A", "app.tasks.celery_app", "worker", "--loglevel=info"]
    logging:
      driver: awslogs
      options:
        awslogs-region: ap-south-1
        awslogs-group: /docker/threatmesh-worker
        awslogs-stream: threatmesh-worker

networks:
  threatmesh-network:
    driver: bridge

volumes:
  redis_data:
  app_logs:
  worker_logs:
