# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

ThreatMesh is a comprehensive FastAPI service for Attack Surface Mapping (ASM) and Cyber Threat Intelligence (CTI) operations. It provides REST API endpoints for security research and monitoring tasks including subdomain enumeration, vulnerability scanning, dark web monitoring, and git repository secret scanning.

## Development Commands

### Running the Application

**Docker Compose (Recommended)**:
```bash
# Start all services (API, Worker, MongoDB, Redis)
docker-compose up --build -d

# View logs
docker-compose logs -f api
docker-compose logs -f worker

# Stop services
docker-compose down
```

**Manual Development**:
```bash
# Start API server with auto-reload
python run.py
# OR
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

# Start Celery worker (separate terminal)
python -m app.worker
# OR
python -m celery -A app.tasks.celery_app worker --loglevel=info
```

### Testing

```bash
# Run all tests
python run_tests.py

# Run with pytest directly
python -m pytest tests/ -v

# Run with coverage
python -m pytest --cov=app tests/
```

### Database Management

```bash
# Initialize database (creates indexes)
python -c "from app.db.operations import init_db; init_db()"
```

## Architecture

### Core Components

- **FastAPI App** (`app/main.py`): Main application with API routes, CORS, and API key authentication
- **Celery Worker** (`app/worker.py`, `app/tasks/celery_app.py`): Background task processing using Redis as message broker
- **MongoDB Operations** (`app/db/operations.py`): Database operations for storing task results and status
- **Router Structure**:
  - `app/routers/asm/`: Attack Surface Mapping endpoints (subdomains, IPs, tech detection, vulnerabilities)
  - `app/routers/cti/`: Cyber Threat Intelligence endpoints (dark web, git search, pastes, phishing)

### Task System

- **Models** (`app/models/`): Pydantic models for request/response validation
- **Tasks** (`app/tasks/`): Celery task implementations mirroring router structure
- **Caching**: Results cached in MongoDB with configurable expiration times (`app/config.py`)
- **Progress Tracking**: Real-time task progress via combined MongoDB + Celery status

### Key Features

**ASM (Attack Surface Mapping)**:
- Subdomain enumeration using subfinder/httpx
- Public IP discovery
- Webserver detection
- Technology stack analysis (Wappalyzer)

**CTI (Cyber Threat Intelligence)**:
- Dark web leak monitoring
- Git repository secret scanning (git-hound)
- Paste site monitoring
- Phishing domain detection

## Environment Configuration

Create `.env` file with required API keys:
```bash
cp env.example .env
```

Required environment variables:
- `API_KEY`: API authentication key
- `MONGODB_URI`: MongoDB connection string
- `REDIS_URI`: Redis connection string
- Various third-party API keys (see GitHub Actions for full list)

## API Usage

All endpoints require `X-API-Key` header for authentication.

**Health Check**: `GET /health`
**API Documentation**: `GET /docs`

**Example requests**:
```bash
# Subdomain enumeration
curl -H "X-API-Key: your-key" "http://localhost:8000/asm/subdomain-enum?domain=example.com"

# Git repository search
curl -H "X-API-Key: your-key" "http://localhost:8000/cti/git-search?query=example.com"
```

## Security Tools Integration

The application integrates with multiple security tools via Docker containers:
- **subfinder**: Subdomain enumeration
- **httpx**: HTTP probe and analysis
- **git-hound**: Git repository secret scanning
- **Wappalyzer**: Technology detection

## Database Schema

- **operations collection**: Stores task metadata, status, and results
- **pastes_search collection**: Chunked paste site search results
- **git_search collection**: Chunked git search results  
- **darkweb_leaks collection**: Chunked dark web leak results

## Development Notes

- Uses MongoDB for persistent storage with indexed collections
- Redis serves as both Celery message broker and caching layer
- Task results are cached with feature-specific expiration times
- Progress tracking combines MongoDB persistence with real-time Celery status
- All security scans run in isolated containers for safety
- Results are paginated for large datasets using chunked storage