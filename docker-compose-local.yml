services:
  # MongoDB Database
  # mongodb:
  #   image: mongo:7.0
  #   container_name: threatmesh-mongodb
  #   restart: unless-stopped
  #   ports:
  #     - "27017:27017"
  #   volumes:
  #     - mongodb_data:/data/db
  #   environment:
  #     - MONGO_INITDB_DATABASE=threatmesh
  #   networks:
  #     - threatmesh-network
  #   healthcheck:
  #     test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3

  # Redis (Message Broker & Cache)
  redis:
    image: redis:7.2-alpine
    container_name: threatmesh-redis
    restart: "no"
    profiles:
      - redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - threatmesh-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    command: redis-server --appendonly yes

  # FastAPI Application
  api:
    build: 
      context: .
      tags:
        - threatmesh:latest
    image: threatmesh:latest
    container_name: threatmesh-api
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      - MONGODB_URI=${MONGODB_URI:-}
      - REDIS_URI=redis://redis:6379/0
      - NVD_API_KEY=${NVD_API_KEY:-}
      - API_KEY=${API_KEY:-default-api-key}
      - GOOGLE_CSE_API_KEY=${GOOGLE_CSE_API_KEY:-}
      - GIT_TOKENS=${GIT_TOKENS:-}
      - ENVIRONMENT=${ENVIRONMENT:-production}
      - LOG_LEVEL=${LOG_LEVEL:-DEBUG}
    # volumes:
      # - ./app:/app/app:ro
      # - app_logs:/app/logs
    networks:
      - threatmesh-network
    depends_on:
      redis:
        condition: service_healthy
        required: false
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 3
    command: ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--log-level", "debug"]

  # Celery Worker
  worker:
    image: threatmesh:latest
    container_name: threatmesh-worker
    restart: unless-stopped
    environment:
      - MONGODB_URI=${MONGODB_URI:-}
      - REDIS_URI=redis://redis:6379/0
      - NVD_API_KEY=${NVD_API_KEY:-}
      - API_KEY=${API_KEY:-default-api-key}
      - GOOGLE_CSE_API_KEY=${GOOGLE_CSE_API_KEY:-}
      - GIT_TOKENS=${GIT_TOKENS:-}
      - ENVIRONMENT=${ENVIRONMENT:-production}
      - LOG_LEVEL=${LOG_LEVEL:-DEBUG}
      - DOCKER_HOST=unix:///var/run/docker.sock
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - /usr/bin/docker:/usr/bin/docker
      # - ./app:/app/app:ro
      # - worker_logs:/app/logs
    networks:
      - threatmesh-network
    depends_on:
      redis:
        condition: service_healthy
        required: false
      api:
        condition: service_started
    command: ["python", "-m", "celery", "-A", "app.tasks.celery_app", "worker", "--loglevel=debug"]



networks:
  threatmesh-network:
    driver: bridge

volumes:
  redis_data:
    driver: local
  app_logs:
    driver: local
  worker_logs:
    driver: local