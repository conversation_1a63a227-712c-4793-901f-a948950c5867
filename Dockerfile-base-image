FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Using root user for all operations
LABEL maintainer="curlsek"

# Install system dependencies organized by tool/feature support
RUN apt-get update && \
    apt-get install -y --fix-missing \
    # Basic build and system tools
    build-essential \
    curl \
    wget \
    unzip \
    git \
    ca-certificates \
    lsb-release \
    jq \
    # Node.js ecosystem (required for Wappalyzer)
    nodejs \
    npm \
    # Python development tools
    python3-dev \
    # Chrome/Puppeteer dependencies (required for Wappalyzer technology detection)
    fonts-liberation \
    libasound2 \
    libatk-bridge2.0-0 \
    libatk1.0-0 \
    libc6 \
    libcairo2 \
    libcups2 \
    libdbus-1-3 \
    libexpat1 \
    libfontconfig1 \
    libgbm1 \
    libgcc1 \
    libglib2.0-0 \
    libgtk-3-0 \
    libnspr4 \
    libnss3 \
    libpango-1.0-0 \
    libpangocairo-1.0-0 \
    libstdc++6 \
    libx11-6 \
    libx11-xcb1 \
    libxcb1 \
    libxcomposite1 \
    libxcursor1 \
    libxdamage1 \
    libxext6 \
    libxfixes3 \
    libxi6 \
    libxrandr2 \
    libxrender1 \
    libxss1 \
    libxtst6 \
    xdg-utils \
    && rm -rf /var/lib/apt/lists/*

# Install security tools from pre-compiled binaries
RUN wget -O /tmp/subfinder.zip https://github.com/projectdiscovery/subfinder/releases/download/v2.8.0/subfinder_2.8.0_linux_amd64.zip && \
    unzip /tmp/subfinder.zip && \
    mv subfinder /opt/ && \
    chmod +x /opt/subfinder && \
    rm /tmp/subfinder.zip && \
    wget -O /tmp/httpx.zip https://github.com/projectdiscovery/httpx/releases/download/v1.7.0/httpx_1.7.0_linux_amd64.zip && \
    unzip /tmp/httpx.zip && \
    mv httpx /opt/ && \
    chmod +x /opt/httpx && \
    rm /tmp/httpx.zip

# Install Wappalyzer CLI
RUN npm install -g yarn

RUN cd /opt/ && \
    git clone https://github.com/MucahidAydin/wappalyzer.git && \
    cd wappalyzer && \
    npm install && \
    yarn run link

# Set Chrome flags for Docker environment
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=false
ENV PUPPETEER_ARGS="--no-sandbox --disable-setuid-sandbox --disable-dev-shm-usage --disable-gpu --no-first-run --no-zygote --single-process"
