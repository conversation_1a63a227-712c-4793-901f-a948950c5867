import pytest
from unittest.mock import patch, MagicMock
from app.routers.asm import create_task_response
from app.models.task import TaskStatus

def test_error_includes_raw_output():
    """Test that error field includes raw output when task fails"""
    # Mock task with failed status
    task = {
        "_id": "test_job_id",
        "status": TaskStatus.FAILED,
        "feature": "test_feature",
        "error": "Test error message"
    }
    
    # Mock result with raw output
    result = {
        "job_id": "test_job_id",
        "raw_output": [
            "Line 1 of raw output",
            "Line 2 of raw output",
            "Line 3 of raw output"
        ]
    }
    
    # Call the function
    response = create_task_response(task, result)
    
    # Verify that error field includes both the error message and raw output
    assert "Test error message" in response.error
    assert "Line 1 of raw output" in response.error
    assert "Line 2 of raw output" in response.error
    assert "Line 3 of raw output" in response.error
    assert "Raw output:" in response.error

def test_error_without_raw_output():
    """Test that error field works when no raw output is available"""
    # Mock task with failed status but no result
    task = {
        "_id": "test_job_id",
        "status": TaskStatus.FAILED,
        "feature": "test_feature",
        "error": "Test error message"
    }
    
    # Call the function with no result
    response = create_task_response(task)
    
    # Verify that error field includes just the error message
    assert response.error == "Test error message"
    assert "Raw output:" not in response.error

def test_error_with_empty_raw_output():
    """Test that error field works when raw output is empty"""
    # Mock task with failed status
    task = {
        "_id": "test_job_id",
        "status": TaskStatus.FAILED,
        "feature": "test_feature",
        "error": "Test error message"
    }
    
    # Mock result with empty raw output
    result = {
        "job_id": "test_job_id",
        "raw_output": []
    }
    
    # Call the function
    response = create_task_response(task, result)
    
    # Verify that error field includes just the error message
    assert response.error == "Test error message"
    assert "Raw output:" not in response.error
