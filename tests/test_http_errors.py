import pytest
from unittest.mock import patch, MagicMock
import requests
from app.tasks.asm_tasks import run_public_ip_search
from app.models.task import TaskStatus
from app.models.database import tasks_collection, results_collection

@pytest.fixture
def mock_collections():
    """Mock MongoDB collections"""
    with patch('app.tasks.asm_tasks.tasks_collection') as mock_tasks, \
         patch('app.tasks.asm_tasks.results_collection') as mock_results:

        # Setup mock find_one to return None (no existing task/result)
        mock_results.find_one.return_value = None

        yield mock_tasks, mock_results

@pytest.fixture
def mock_requests():
    """Mock requests.get to simulate HTTP errors"""
    with patch('app.tasks.asm_tasks.requests.get') as mock_get:
        # Setup mock to raise ConnectionError for all requests
        mock_get.side_effect = requests.exceptions.ConnectionError("Connection refused")
        yield mock_get

def test_http_errors_marked_as_failure(mock_collections, mock_requests):
    """Test that HTTP errors are properly marked as task failures"""
    mock_tasks, mock_results = mock_collections

    # Run the task
    result = run_public_ip_search("example.com", "test_job_id")

    # Verify that the task was marked as failed
    assert result["success"] is False
    assert "All Shodan queries failed" in result["message"]

    # Verify that the task status was updated to FAILED
    # Check that update_one was called with the right job_id and status
    found_failed_update = False
    for call_args in mock_tasks.update_one.call_args_list:
        args, kwargs = call_args
        if args[0] == {"_id": "test_job_id"} and \
           "$set" in args[1] and \
           args[1]["$set"].get("status") == TaskStatus.FAILED and \
           args[1]["$set"].get("error") == "All Shodan queries failed":
            found_failed_update = True
            break

    assert found_failed_update, "Task was not marked as failed with the correct error message"

    # Verify that raw_output was updated with error messages
    found_raw_output_update = False
    for call_args in mock_results.update_one.call_args_list:
        args, kwargs = call_args
        if args[0] == {"job_id": "test_job_id"} and \
           "$set" in args[1] and \
           "raw_output" in args[1]["$set"] and \
           isinstance(args[1]["$set"]["raw_output"], list) and \
           len(args[1]["$set"]["raw_output"]) > 0:
            found_raw_output_update = True
            break

    assert found_raw_output_update, "Raw output was not updated with error messages"

@pytest.fixture
def mock_partial_success():
    """Mock requests.get to simulate partial success (some queries succeed, some fail)"""
    with patch('app.tasks.asm_tasks.requests.get') as mock_get:
        # Create a mock response for successful requests
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.text = '<div class="four columns name"><strong>192.168.1.1</strong></div>'

        # First call succeeds, second call fails
        mock_get.side_effect = [
            mock_response,  # First Shodan query succeeds
            requests.exceptions.ConnectionError("Connection refused"),  # Second Shodan query fails
            mock_response,  # InternetDB query succeeds
        ]

        yield mock_get

def test_partial_http_errors_continue(mock_collections, mock_partial_success):
    """Test that task continues if at least one HTTP query succeeds"""
    mock_tasks, mock_results = mock_collections

    # Run the task
    result = run_public_ip_search("example.com", "test_job_id")

    # Verify that the task was marked as successful
    assert result["success"] is True
    assert "Found 1 IPs for domain example.com" in result["message"]

    # Verify that the task status was updated to COMPLETED
    # Check that update_one was called with the right job_id and status
    found_completed_update = False
    for call_args in mock_tasks.update_one.call_args_list:
        args, kwargs = call_args
        if args[0] == {"_id": "test_job_id"} and \
           "$set" in args[1] and \
           args[1]["$set"].get("status") == TaskStatus.COMPLETED:
            found_completed_update = True
            break

    assert found_completed_update, "Task was not marked as completed"
