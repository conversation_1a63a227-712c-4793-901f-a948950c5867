import pytest
from fastapi import HTT<PERSON>Exception
from app.validators import validate_domain_param, DomainValidator

def test_domain_validator_valid():
    """Test valid domain names"""
    valid_domains = [
        "example.com",
        "sub.example.com",
        "sub-domain.example.co.uk",
        "example-domain.com",
        "123example.com",
    ]

    for domain in valid_domains:
        # Should not raise an exception
        validator = DomainValidator(domain=domain)
        assert validator.domain == domain

def test_domain_validator_invalid():
    """Test invalid domain names"""
    invalid_domains = [
        "",  # Empty
        "example",  # No TLD
        "example.c",  # TLD too short
        "example..com",  # Double dot
        "-example.com",  # Starts with hyphen
        "example-.com",  # Ends with hyphen
        "exam ple.com",  # Contains space
        "example.com/path",  # Contains path
        "http://example.com",  # Contains scheme
    ]

    for domain in invalid_domains:
        with pytest.raises(ValueError):
            DomainValidator(domain=domain)

def test_validate_domain_param_with_domain():
    """Test validate_domain_param with valid domain"""
    domain, job_id, ignore_cache = validate_domain_param(domain="example.com", job_id=None)
    assert domain == "example.com"
    assert job_id is None
    # For FastAPI Query objects, we need to check the default value
    assert ignore_cache.default == False

def test_validate_domain_param_with_job_id():
    """Test validate_domain_param with job_id"""
    domain, job_id, ignore_cache = validate_domain_param(domain=None, job_id="123456")
    assert domain is None
    assert job_id == "123456"
    # For FastAPI Query objects, we need to check the default value
    assert ignore_cache.default == False

def test_validate_domain_param_with_both():
    """Test validate_domain_param with both domain and job_id"""
    domain, job_id, ignore_cache = validate_domain_param(domain="example.com", job_id="123456")
    assert domain == "example.com"
    assert job_id == "123456"
    # For FastAPI Query objects, we need to check the default value
    assert ignore_cache.default == False

def test_validate_domain_param_with_neither():
    """Test validate_domain_param with neither domain nor job_id"""
    with pytest.raises(HTTPException) as excinfo:
        validate_domain_param(domain=None, job_id=None)

    assert excinfo.value.status_code == 400
    assert "Either 'domain' or 'job_id' parameter must be provided" in excinfo.value.detail

def test_validate_domain_param_with_invalid_domain():
    """Test validate_domain_param with invalid domain"""
    with pytest.raises(HTTPException) as excinfo:
        validate_domain_param(domain="invalid", job_id=None)

    assert excinfo.value.status_code == 400
    assert "Invalid domain format" in excinfo.value.detail

def test_validate_domain_param_with_ignore_cache():
    """Test validate_domain_param with ignore_cache parameter"""
    # When we pass True directly, it should be passed through
    domain, job_id, ignore_cache = validate_domain_param(domain="example.com", job_id=None, ignore_cache=True)
    assert domain == "example.com"
    assert job_id is None
    assert ignore_cache == True



def test_validate_domain_param_with_all_params():
    """Test validate_domain_param with all parameters"""
    domain, job_id, ignore_cache = validate_domain_param(
        domain="example.com", job_id="123456", ignore_cache=True
    )
    assert domain == "example.com"
    assert job_id == "123456"
    assert ignore_cache == True
