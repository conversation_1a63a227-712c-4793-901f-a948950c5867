import os
import requests
import time
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# API URL
API_URL = "http://localhost:8000"
API_KEY = os.getenv("API_KEY", "your_secret_api_key")

# Headers
headers = {
    "X-API-Key": API_KEY
}

def test_root():
    """Test root endpoint"""
    response = requests.get(f"{API_URL}/")
    assert response.status_code == 200
    assert response.json() == {"message": "Welcome to ThreatMesh API"}

def test_subdomain_enumeration():
    """Test subdomain enumeration endpoint"""
    # Start task
    response = requests.get(
        f"{API_URL}/asm/subdomains",
        params={"domain": "example.com"},
        headers=headers
    )
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "running"
    assert "job_id" in data

    # Get task ID
    job_id = data["job_id"]

    # Poll for results (max 30 seconds)
    for _ in range(30):
        response = requests.get(
            f"{API_URL}/asm/subdomains",
            params={"domain": "example.com", "job_id": job_id},
            headers=headers
        )
        assert response.status_code == 200
        data = response.json()

        if data["status"] == "completed":
            assert "results" in data
            assert "subdomains" in data["results"]
            assert isinstance(data["results"]["subdomains"], list)
            # Check for sources
            assert "sources" in data["results"]
            assert isinstance(data["results"]["sources"], dict)
            # Check count
            assert "count" in data["results"]
            assert data["results"]["count"] == len(data["results"]["subdomains"])
            break

        time.sleep(1)
    else:
        assert False, "Task did not complete in time"

def test_port_scan():
    """Test port scan endpoint"""
    # Start task
    response = requests.get(
        f"{API_URL}/asm/ports",
        params={"target": "example.com"},
        headers=headers
    )
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "running"
    assert "job_id" in data

    # Get task ID
    job_id = data["job_id"]

    # Poll for results (max 10 seconds)
    for _ in range(10):
        response = requests.get(
            f"{API_URL}/asm/ports",
            params={"target": "example.com", "job_id": job_id},
            headers=headers
        )
        assert response.status_code == 200
        data = response.json()

        if data["status"] == "completed":
            assert "results" in data
            # Check for the placeholder message
            assert "message" in data["results"]
            assert "target" in data["results"]
            assert "ports" in data["results"]
            assert data["results"]["message"] == "Port scanning not yet implemented"
            assert data["results"]["target"] == "example.com"
            break

        time.sleep(1)
    else:
        assert False, "Task did not complete in time"

    """Test darkweb search endpoint"""
    # Start task
    response = requests.get(
        f"{API_URL}/cti/darkweb",
        params={"keyword": "example.com"},
        headers=headers
    )
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "running"
    assert "job_id" in data

    # Get task ID
    job_id = data["job_id"]

    # Poll for results (max 10 seconds)
    for _ in range(10):
        response = requests.get(
            f"{API_URL}/cti/darkweb",
            params={"keyword": "example.com", "job_id": job_id},
            headers=headers
        )
        assert response.status_code == 200
        data = response.json()

        if data["status"] == "completed":
            assert "results" in data
            assert "mentions" in data["results"]
            assert isinstance(data["results"]["mentions"], list)
            break

        time.sleep(1)
    else:
        assert False, "Task did not complete in time"

if __name__ == "__main__":
    print("Testing API...")

    try:
        test_root()
        print("✅ Root endpoint test passed")

        test_subdomain_enumeration()
        print("✅ Subdomain enumeration test passed")

        print("All tests passed!")
    except Exception as e:
        print(f"❌ Test failed: {e}")
